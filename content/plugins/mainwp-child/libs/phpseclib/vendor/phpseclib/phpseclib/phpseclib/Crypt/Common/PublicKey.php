<?php

/**
 * Public<PERSON>ey interface
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2009 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

namespace phpseclib3\Crypt\Common;

/**
 * PublicKey interface
 *
 * <AUTHOR> <<EMAIL>>
 */
interface PublicKey
{
    public function verify($message, $signature);
    //public function encrypt($plaintext);
    public function toString($type, array $options = []);
    public function getFingerprint($algorithm);
}
