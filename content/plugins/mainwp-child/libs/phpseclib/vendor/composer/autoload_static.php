<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInite16285058e0be038fc2e64fc06785368
{
    public static $files = array (
        'decc78cc4436b1292c6c0d151b19445c' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/bootstrap.php',
    );

    public static $prefixLengthsPsr4 = array (
        'p' => 
        array (
            'phpseclib3\\' => 11,
        ),
        'P' => 
        array (
            'ParagonIE\\ConstantTime\\' => 23,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'phpseclib3\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
        ),
        'ParagonIE\\ConstantTime\\' => 
        array (
            0 => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInite16285058e0be038fc2e64fc06785368::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInite16285058e0be038fc2e64fc06785368::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInite16285058e0be038fc2e64fc06785368::$classMap;

        }, null, ClassLoader::class);
    }
}
