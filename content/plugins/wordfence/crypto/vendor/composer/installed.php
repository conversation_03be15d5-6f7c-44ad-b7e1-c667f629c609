<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '4dbf15a23f788f9f3a3e57f5971bf957e5f9ba01',
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '4dbf15a23f788f9f3a3e57f5971bf957e5f9ba01',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v2.0.21',
            'version' => '2.0.21.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '96c132c7f2f7bc3230723b66e89f8f150b29d5ae',
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'reference' => 'e592a3e06d1fa0d43988c7c7d9948ca836f644b6',
            'dev_requirement' => false,
        ),
    ),
);
