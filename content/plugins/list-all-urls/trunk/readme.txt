=== List all URLs ===
Contributors: evster, psykro
Tags: URLs
Requires at least: 3.0.1
Tested up to: 6.8.1
Stable Tag: 1.0.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Creates a page in the admin panel under Settings > List All URLs that outputs an ordered list of all of the website's published URLs.

== Description ==

This plugin will add a page to the admin panel that displays a numerically ordered list of all published URLs within a website.

Now, why would such a feature be helpful?

First off, it allows to to quickly check all of your website URLs to ensure they are using valid characters (i.e no % symbols that may have been entered by copying and pasting from another document).

Secondly, let's say you're planning on moving your website to a new URL, or perhaps you would like to update many of your website's existing URLs for better SEO or accessibility. This plugin give you a quick and easy way to view all website URLs on one single screen and can serve as a great starting point for creating a list of 301 redirects as part of an .htaccess file.

NEW FOR VERSION .2

Now you can customize your list of URLs by page, post, custom post type, or simply get all URLs like in the original version.

There is also a new option which can turn the list of generated URLs into clickable hyperlinks.

If you spot a bug or have an idea of how I can improve my code please let me know.

== Installation ==

1. Upload `list-all-urls.php` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to `Settings > List all URLs` within the admin panel to generate an ordered list of all website URLs.

== Frequently Asked Questions ==

= Where can I find support? =

Post a question in the support forum for this plugin and I will try to answer as soon as possible.

== Screenshots ==
The List All URLs admin page

== Changelog ==

= 1.0.0 =
Complete rewrite of core functionality
Added support for Feature API

= 0.2.1 =
Tested on WordPress 5.2.3 and PHP 7.3

= 0.2 =
Added options to sort URLs by post, pages, custom post types, or everything
Added option to turn list of all URLs into clickable hyperlinks

= 0.1 =
Initial release
