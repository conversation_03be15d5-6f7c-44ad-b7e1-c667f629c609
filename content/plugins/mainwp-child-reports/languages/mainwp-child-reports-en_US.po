msgid ""
msgstr ""
"Project-Id-Version: Stream\n"
"POT-Creation-Date: 2015-10-02 11:57+1000\n"
"PO-Revision-Date: 2015-10-02 11:57+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.6.3\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"

#: stream.php:47
msgid "Stream requires PHP version 5.3+, plugin is currently NOT ACTIVE."
msgstr ""

#: classes/class-admin.php:195
msgid "All site settings have been successfully reset."
msgstr ""

#: classes/class-admin.php:270 classes/class-network.php:122
#: connectors/class-connector-settings.php:134
msgid "Stream"
msgstr ""

#: classes/class-admin.php:286
msgid "Stream Records"
msgstr ""

#: classes/class-admin.php:303
msgid "Stream Settings"
msgstr ""

#: classes/class-admin.php:308 classes/class-admin.php:662
#: connectors/class-connector-settings.php:105
#: connectors/class-connector-settings.php:126
#: connectors/class-connector-woocommerce.php:728
msgid "Settings"
msgstr ""

#: classes/class-admin.php:369
msgid ""
"Are you sure you want to delete all Stream activity records from the "
"database? This cannot be undone."
msgstr ""

#: classes/class-admin.php:370
msgid ""
"Are you sure you want to reset all site settings to default? This cannot be "
"undone."
msgstr ""

#: classes/class-admin.php:371
msgid ""
"Are you sure you want to uninstall and deactivate Stream? This will delete "
"all Stream tables from the database and cannot be undone."
msgstr ""

#: classes/class-admin.php:412
#, php-format
msgid ""
"Are you sure you want to perform bulk actions on over %s items? This process "
"could take a while to complete."
msgstr ""

#: classes/class-admin.php:546 classes/class-uninstall.php:51
msgid "You don't have sufficient privileges to do this action."
msgstr ""

#: classes/class-admin.php:672
msgid "Uninstall"
msgstr ""

#: classes/class-author.php:82 classes/class-author.php:90
#: classes/class-settings.php:779
msgid "N/A"
msgstr ""

#: classes/class-author.php:290
msgid "via WP-CLI"
msgstr ""

#: classes/class-author.php:292
msgid "during WP Cron"
msgstr ""

#: classes/class-cli.php:218
msgid "SITE IS DISCONNECTED"
msgstr ""

#: classes/class-connectors.php:123
#, php-format
msgid ""
"%s class wasn't loaded because it doesn't implement the get_label method."
msgstr ""

#: classes/class-connectors.php:127
#, php-format
msgid ""
"%s class wasn't loaded because it doesn't implement the register method."
msgstr ""

#: classes/class-connectors.php:131
#, php-format
msgid ""
"%s class wasn't loaded because it doesn't implement the get_context_labels "
"method."
msgstr ""

#: classes/class-connectors.php:135
#, php-format
msgid ""
"%s class wasn't loaded because it doesn't implement the get_action_labels "
"method."
msgstr ""

#: classes/class-connectors.php:141
#, php-format
msgid "%s class wasn't loaded because it doesn't extends the %s class."
msgstr ""

#: classes/class-date-interval.php:48
msgid "Today"
msgstr ""

#: classes/class-date-interval.php:53
msgid "Yesterday"
msgstr ""

#: classes/class-date-interval.php:58 classes/class-date-interval.php:63
#: classes/class-date-interval.php:68
#, php-format
msgid "Last %d Days"
msgstr ""

#: classes/class-date-interval.php:73
msgid "This Month"
msgstr ""

#: classes/class-date-interval.php:78
msgid "Last Month"
msgstr ""

#: classes/class-date-interval.php:83 classes/class-date-interval.php:88
#: classes/class-date-interval.php:93
#, php-format
msgid "Last %d Months"
msgstr ""

#: classes/class-date-interval.php:98
msgid "This Year"
msgstr ""

#: classes/class-date-interval.php:103
msgid "Last Year"
msgstr ""

#: classes/class-filter-input.php:51
msgid "Invalid use, type must be one of INPUT_* family."
msgstr ""

#: classes/class-filter-input.php:67
msgid "Filter not supported."
msgstr ""

#: classes/class-install.php:175
msgid "The following table is not present in the WordPress database:"
msgid_plural "The following tables are not present in the WordPress database:"
msgstr[0] ""
msgstr[1] ""

#: classes/class-install.php:185 classes/class-install.php:187
#, php-format
msgid ""
"Please <a href=\"%s\">uninstall</a> the Stream plugin and activate it again."
msgstr ""

#: classes/class-install.php:261
msgid "There was an error updating the Stream database. Please try again."
msgstr ""

#: classes/class-install.php:262
msgid "Database Update Error"
msgstr ""

#: classes/class-install.php:309
msgid "Stream Database Update Required"
msgstr ""

#: classes/class-install.php:310
msgid ""
"Stream has updated! Before we send you on your way, we need to update your "
"database to the newest version."
msgstr ""

#: classes/class-install.php:311
msgid "This process could take a little while, so please be patient."
msgstr ""

#: classes/class-install.php:312
msgid "Update Database"
msgstr ""

#: classes/class-install.php:331
msgid "Update Complete"
msgstr ""

#: classes/class-install.php:333
msgid "Continue"
msgstr ""

#: classes/class-list-table.php:41
msgid "Records per page"
msgstr ""

#: classes/class-list-table.php:64
msgid "Sorry, no activity records were found."
msgstr ""

#: classes/class-list-table.php:78
msgid "Date"
msgstr ""

#: classes/class-list-table.php:79
msgid "Summary"
msgstr ""

#: classes/class-list-table.php:80
msgid "User"
msgstr ""

#: classes/class-list-table.php:81 classes/class-settings.php:737
msgid "Context"
msgstr ""

#: classes/class-list-table.php:82 classes/class-settings.php:738
msgid "Action"
msgstr ""

#: classes/class-list-table.php:83 classes/class-settings.php:739
msgid "IP Address"
msgstr ""

#: classes/class-list-table.php:241
#, php-format
msgid "View all activity for \"%s\""
msgstr ""

#: classes/class-list-table.php:241
msgid "View all activity for this object"
msgstr ""

#: classes/class-list-table.php:265
msgid "Deleted User"
msgstr ""

#: classes/class-list-table.php:506
msgid "dates"
msgstr ""

#: classes/class-list-table.php:515
msgid "users"
msgstr ""

#: classes/class-list-table.php:521
msgid "contexts"
msgstr ""

#: classes/class-list-table.php:526
msgid "actions"
msgstr ""

#: classes/class-list-table.php:545
msgid "Show filter controls via the screen options tab above."
msgstr ""

#: classes/class-list-table.php:594
msgid "Filter"
msgstr ""

#: classes/class-list-table.php:619
msgid "Reset filters"
msgstr ""

#: classes/class-list-table.php:670
#, php-format
msgid "Show all %s"
msgstr ""

#: classes/class-list-table.php:716
msgid "Search Records"
msgstr ""

#: classes/class-list-table.php:736
msgid "All Time"
msgstr ""

#: classes/class-list-table.php:738
msgid "Custom"
msgstr ""

#: classes/class-list-table.php:758
msgid "Start Date"
msgstr ""

#: classes/class-list-table.php:764
msgid "End Date"
msgstr ""

#: classes/class-list-table.php:856
msgid "Live updates"
msgstr ""

#: classes/class-list-table.php:867 classes/class-network.php:271
#: classes/class-settings.php:251 classes/class-settings.php:277
#: classes/class-settings.php:307 classes/class-settings.php:321
msgid "Enabled"
msgstr ""

#: classes/class-live-update.php:67
msgid ""
"Live updates could not be enabled because Heartbeat is not loaded.\n"
"\n"
"Your hosting provider or another plugin may have disabled it for performance "
"reasons."
msgstr ""

#: classes/class-live-update.php:187
#, php-format
msgid "1 item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: classes/class-network.php:78
msgid "Network Admin"
msgstr ""

#: classes/class-network.php:142
msgid "Stream Network Settings"
msgstr ""

#: classes/class-network.php:143
msgid "Network Settings"
msgstr ""

#: classes/class-network.php:197
msgid "These settings apply to all sites on the network."
msgstr ""

#: classes/class-network.php:200
msgid ""
"These default settings will apply to new sites created on the network. These "
"settings do not alter existing sites."
msgstr ""

#: classes/class-network.php:270
msgid "Site Access"
msgstr ""

#: classes/class-network.php:273
msgid ""
"Allow sites on this network to view their Stream activity. Leave unchecked "
"to only allow Stream to be viewed in the Network Admin."
msgstr ""

#: classes/class-network.php:360
msgid "Settings saved."
msgstr ""

#: classes/class-network.php:407
msgid "sites"
msgstr ""

#: classes/class-network.php:423 classes/class-network.php:501
msgid "Site"
msgstr ""

#: classes/class-network.php:482
#, php-format
msgid "1 site"
msgid_plural "%s sites"
msgstr[0] ""
msgstr[1] ""

#: classes/class-plugin.php:83
msgid "Stream: Could not load chosen DB driver."
msgstr ""

#: classes/class-plugin.php:84
msgid "Stream DB Error"
msgstr ""

#: classes/class-record.php:43
msgid "Could not validate record data."
msgstr ""

#: classes/class-settings.php:84
msgid "There was an error in the request"
msgstr ""

#: classes/class-settings.php:132
#, php-format
msgid ""
"ID: %d\n"
"User: %s\n"
"Email: %s\n"
"Role: %s"
msgstr ""

#: classes/class-settings.php:151
msgid ""
"Actions performed by the system when a user is not logged in (e.g. auto site "
"upgrader, or invoking WP-CLI without --user)"
msgstr ""

#: classes/class-settings.php:224 connectors/class-connector-settings.php:127
msgid "General"
msgstr ""

#: classes/class-settings.php:228
msgid "Role Access"
msgstr ""

#: classes/class-settings.php:230
msgid ""
"Users from the selected roles above will have permission to view Stream "
"Records. However, only site Administrators can access Stream Settings."
msgstr ""

#: classes/class-settings.php:236
msgid "Keep Records for"
msgstr ""

#: classes/class-settings.php:239
msgid "Maximum number of days to keep activity records."
msgstr ""

#: classes/class-settings.php:244
msgid "days"
msgstr ""

#: classes/class-settings.php:248
msgid "Keep Records Indefinitely"
msgstr ""

#: classes/class-settings.php:250
msgid "Not recommended."
msgstr ""

#: classes/class-settings.php:250
msgid ""
"Purging old records helps to keep your WordPress installation running "
"optimally."
msgstr ""

#: classes/class-settings.php:257
msgid "Exclude"
msgstr ""

#: classes/class-settings.php:261
msgid "Exclude Rules"
msgstr ""

#: classes/class-settings.php:263
msgid ""
"Create rules to exclude certain kinds of activity from being recorded by "
"Stream."
msgstr ""

#: classes/class-settings.php:270
msgid "Advanced"
msgstr ""

#: classes/class-settings.php:274
msgid "Comment Flood Tracking"
msgstr ""

#: classes/class-settings.php:276
msgid ""
"WordPress will automatically prevent duplicate comments from flooding the "
"database. By default, Stream does not track these attempts unless you opt-in "
"here. Enabling this is not necessary or recommended for most sites."
msgstr ""

#: classes/class-settings.php:282
msgid "Reset Stream Database"
msgstr ""

#: classes/class-settings.php:292
msgid "Warning: This will delete all activity records from the database."
msgstr ""

#: classes/class-settings.php:304
msgid "Akismet Tracking"
msgstr ""

#: classes/class-settings.php:306
msgid ""
"Akismet already keeps statistics for comment attempts that it blocks as "
"SPAM. By default, Stream does not track these attempts unless you opt-in "
"here. Enabling this is not necessary or recommended for most sites."
msgstr ""

#: classes/class-settings.php:318
msgid "WP Cron Tracking"
msgstr ""

#: classes/class-settings.php:320
msgid ""
"By default, Stream does not track activity performed by WordPress cron "
"events unless you opt-in here. Enabling this is not necessary or recommended "
"for most sites."
msgstr ""

#: classes/class-settings.php:703
#, php-format
msgid "Any %s"
msgstr ""

#: classes/class-settings.php:718
msgid "Add New Rule"
msgstr ""

#: classes/class-settings.php:719
msgid "Delete Selected Rules"
msgstr ""

#: classes/class-settings.php:736
msgid "Author or Role"
msgstr ""

#: classes/class-settings.php:740
msgid "Filters"
msgstr ""

#: classes/class-settings.php:766
#, php-format
msgid "1 user"
msgid_plural "%s users"
msgstr[0] ""
msgstr[1] ""

#: classes/class-settings.php:792
msgid "Any Author or Role"
msgstr ""

#: classes/class-settings.php:833
msgid "Any Context"
msgstr ""

#: classes/class-settings.php:852
msgid "Any Action"
msgstr ""

#: classes/class-settings.php:863
msgid "Any IP Address"
msgstr ""

#: classes/class-settings.php:900
msgid "No rules found."
msgstr ""

#: connectors/class-connector-acf.php:74
msgctxt "acf"
msgid "ACF"
msgstr ""

#: connectors/class-connector-acf.php:84
#: connectors/class-connector-blogs.php:51
#: connectors/class-connector-comments.php:55
#: connectors/class-connector-menus.php:39
#: connectors/class-connector-posts.php:39
#: connectors/class-connector-taxonomies.php:54
#: connectors/class-connector-users.php:52
#: connectors/class-connector-widgets.php:59
msgid "Created"
msgstr ""

#: connectors/class-connector-acf.php:85
#: connectors/class-connector-blogs.php:53
#: connectors/class-connector-editor.php:54
#: connectors/class-connector-installer.php:49
#: connectors/class-connector-media.php:43
#: connectors/class-connector-menus.php:40
#: connectors/class-connector-posts.php:38
#: connectors/class-connector-settings.php:115
#: connectors/class-connector-taxonomies.php:55
#: connectors/class-connector-users.php:51
#: connectors/class-connector-widgets.php:63
msgid "Updated"
msgstr ""

#: connectors/class-connector-acf.php:86
#: connectors/class-connector-widgets.php:56
msgid "Added"
msgstr ""

#: connectors/class-connector-acf.php:87
#: connectors/class-connector-blogs.php:52
#: connectors/class-connector-comments.php:64
#: connectors/class-connector-installer.php:48
#: connectors/class-connector-media.php:44
#: connectors/class-connector-menus.php:41
#: connectors/class-connector-posts.php:42
#: connectors/class-connector-taxonomies.php:56
#: connectors/class-connector-users.php:53
#: connectors/class-connector-widgets.php:60
msgid "Deleted"
msgstr ""

#: connectors/class-connector-acf.php:98
msgctxt "acf"
msgid "Field Groups"
msgstr ""

#: connectors/class-connector-acf.php:99
msgctxt "acf"
msgid "Fields"
msgstr ""

#: connectors/class-connector-acf.php:100
msgctxt "acf"
msgid "Rules"
msgstr ""

#: connectors/class-connector-acf.php:101
msgctxt "acf"
msgid "Options"
msgstr ""

#: connectors/class-connector-acf.php:102
msgctxt "acf"
msgid "Values"
msgstr ""

#: connectors/class-connector-acf.php:226
#, php-format
msgctxt "acf"
msgid "\"%1$s\" field in \"%2$s\" %3$s"
msgstr ""

#: connectors/class-connector-acf.php:250
msgctxt "acf"
msgid "High (after title)"
msgstr ""

#: connectors/class-connector-acf.php:251
msgctxt "acf"
msgid "Normal (after content)"
msgstr ""

#: connectors/class-connector-acf.php:252
msgctxt "acf"
msgid "Side"
msgstr ""

#: connectors/class-connector-acf.php:256
#, php-format
msgctxt "acf"
msgid "Position of \"%1$s\" updated to \"%2$s\""
msgstr ""

#: connectors/class-connector-acf.php:273
msgctxt "acf"
msgid "Seamless (no metabox)"
msgstr ""

#: connectors/class-connector-acf.php:274
msgctxt "acf"
msgid "Standard (WP metabox)"
msgstr ""

#: connectors/class-connector-acf.php:278
#, php-format
msgctxt "acf"
msgid "Style of \"%1$s\" updated to \"%2$s\""
msgstr ""

#: connectors/class-connector-acf.php:295
msgctxt "acf"
msgid "Permalink"
msgstr ""

#: connectors/class-connector-acf.php:296
msgctxt "acf"
msgid "Content Editor"
msgstr ""

#: connectors/class-connector-acf.php:297
msgctxt "acf"
msgid "Excerpt"
msgstr ""

#: connectors/class-connector-acf.php:298
msgctxt "acf"
msgid "Custom Fields"
msgstr ""

#: connectors/class-connector-acf.php:299
msgctxt "acf"
msgid "Discussion"
msgstr ""

#: connectors/class-connector-acf.php:300
msgctxt "acf"
msgid "Comments"
msgstr ""

#: connectors/class-connector-acf.php:301
msgctxt "acf"
msgid "Revisions"
msgstr ""

#: connectors/class-connector-acf.php:302
msgctxt "acf"
msgid "Slug"
msgstr ""

#: connectors/class-connector-acf.php:303
msgctxt "acf"
msgid "Author"
msgstr ""

#: connectors/class-connector-acf.php:304
msgctxt "acf"
msgid "Format"
msgstr ""

#: connectors/class-connector-acf.php:305
msgctxt "acf"
msgid "Featured Image"
msgstr ""

#: connectors/class-connector-acf.php:306
msgctxt "acf"
msgid "Categories"
msgstr ""

#: connectors/class-connector-acf.php:307
msgctxt "acf"
msgid "Tags"
msgstr ""

#: connectors/class-connector-acf.php:308
msgctxt "acf"
msgid "Send Trackbacks"
msgstr ""

#: connectors/class-connector-acf.php:312
msgctxt "acf"
msgid "All screens"
msgstr ""

#: connectors/class-connector-acf.php:314
msgctxt "acf"
msgid "No screens"
msgstr ""

#: connectors/class-connector-acf.php:320
#, php-format
msgctxt "acf"
msgid "\"%1$s\" set to display on \"%2$s\""
msgstr ""

#: connectors/class-connector-acf.php:382
msgid "user"
msgstr ""

#: connectors/class-connector-acf.php:395
#, php-format
msgctxt "acf"
msgid "\"%1$s\" of \"%2$s\" %3$s updated"
msgstr ""

#: connectors/class-connector-acf.php:433
#, php-format
msgctxt "acf"
msgid "Updated rules of \"%1$s\" (%2$d added, %3$d deleted)"
msgstr ""

#: connectors/class-connector-acf.php:463
msgid "field group"
msgstr ""

#: connectors/class-connector-acf.php:500
#, php-format
msgctxt "acf"
msgid "\"%1$s\" reordered from %2$d to %3$d"
msgstr ""

#: connectors/class-connector-bbpress.php:78
msgctxt "bbpress"
msgid "bbPress"
msgstr ""

#: connectors/class-connector-bbpress.php:88
msgctxt "bbpress"
msgid "Created"
msgstr ""

#: connectors/class-connector-bbpress.php:89
msgctxt "bbpress"
msgid "Updated"
msgstr ""

#: connectors/class-connector-bbpress.php:90
msgctxt "bbpress"
msgid "Activated"
msgstr ""

#: connectors/class-connector-bbpress.php:91
msgctxt "bbpress"
msgid "Deactivated"
msgstr ""

#: connectors/class-connector-bbpress.php:92
msgctxt "bbpress"
msgid "Deleted"
msgstr ""

#: connectors/class-connector-bbpress.php:93
msgctxt "bbpress"
msgid "Trashed"
msgstr ""

#: connectors/class-connector-bbpress.php:94
msgctxt "bbpress"
msgid "Restored"
msgstr ""

#: connectors/class-connector-bbpress.php:95
msgctxt "bbpress"
msgid "Generated"
msgstr ""

#: connectors/class-connector-bbpress.php:96
msgctxt "bbpress"
msgid "Imported"
msgstr ""

#: connectors/class-connector-bbpress.php:97
msgctxt "bbpress"
msgid "Exported"
msgstr ""

#: connectors/class-connector-bbpress.php:98
msgctxt "bbpress"
msgid "Closed"
msgstr ""

#: connectors/class-connector-bbpress.php:99
msgctxt "bbpress"
msgid "Opened"
msgstr ""

#: connectors/class-connector-bbpress.php:100
msgctxt "bbpress"
msgid "Sticked"
msgstr ""

#: connectors/class-connector-bbpress.php:101
msgctxt "bbpress"
msgid "Unsticked"
msgstr ""

#: connectors/class-connector-bbpress.php:102
msgctxt "bbpress"
msgid "Marked as spam"
msgstr ""

#: connectors/class-connector-bbpress.php:103
msgctxt "bbpress"
msgid "Unmarked as spam"
msgstr ""

#: connectors/class-connector-bbpress.php:114
msgctxt "bbpress"
msgid "Settings"
msgstr ""

#: connectors/class-connector-bbpress.php:131
#: connectors/class-connector-buddypress.php:164
#: connectors/class-connector-buddypress.php:223
#: connectors/class-connector-buddypress.php:233
#: connectors/class-connector-buddypress.php:254
#: connectors/class-connector-comments.php:133
#: connectors/class-connector-gravityforms.php:137
#: connectors/class-connector-wordpress-seo.php:116
#: connectors/class-connector-wordpress-seo.php:123
msgid "Edit"
msgstr ""

#: connectors/class-connector-bbpress.php:165
msgid "Reply Threading"
msgstr ""

#: connectors/class-connector-bbpress.php:187
#, php-format
msgid "Replied on \"%1$s\""
msgstr ""

#: connectors/class-connector-bbpress.php:191
#, php-format
msgid "Reply to: %s"
msgstr ""

#: connectors/class-connector-bbpress.php:233
#, php-format
msgctxt "1: Action, 2: Topic title"
msgid "%1$s \"%2$s\" topic"
msgstr ""

#: connectors/class-connector-blogs.php:40
msgid "Sites"
msgstr ""

#: connectors/class-connector-blogs.php:50
msgid "Archived"
msgstr ""

#: connectors/class-connector-blogs.php:89
#: connectors/class-connector-blogs.php:95
msgid "Site Admin"
msgstr ""

#: connectors/class-connector-blogs.php:106
msgid "Site Settings"
msgstr ""

#: connectors/class-connector-blogs.php:125
#, php-format
msgctxt "1. Site name"
msgid "\"%1$s\" site was created"
msgstr ""

#: connectors/class-connector-blogs.php:151
#, php-format
msgctxt "1. Site name"
msgid "\"%1$s\" site was registered"
msgstr ""

#: connectors/class-connector-blogs.php:184
#, php-format
msgctxt "1. User's name, 2. Site name, 3. Role"
msgid "%1$s was added to the \"%2$s\" site with %3$s capabilities"
msgstr ""

#: connectors/class-connector-blogs.php:217
#, php-format
msgctxt "1. User's name, 2. Site name"
msgid "%1$s was removed from the \"%2$s\" site"
msgstr ""

#: connectors/class-connector-blogs.php:239
#: connectors/class-connector-gravityforms.php:650
msgid "marked as spam"
msgstr ""

#: connectors/class-connector-blogs.php:250
msgid "marked as not spam"
msgstr ""

#: connectors/class-connector-blogs.php:261
msgid "marked as mature"
msgstr ""

#: connectors/class-connector-blogs.php:272
msgid "marked as not mature"
msgstr ""

#: connectors/class-connector-blogs.php:283
msgid "archived"
msgstr ""

#: connectors/class-connector-blogs.php:294
msgid "restored from archive"
msgstr ""

#: connectors/class-connector-blogs.php:305
#: connectors/class-connector-gravityforms.php:497
msgid "deleted"
msgstr ""

#: connectors/class-connector-blogs.php:316
#: connectors/class-connector-gravityforms.php:652
msgid "restored"
msgstr ""

#: connectors/class-connector-blogs.php:329
msgid "marked as public"
msgstr ""

#: connectors/class-connector-blogs.php:331
msgid "marked as private"
msgstr ""

#: connectors/class-connector-blogs.php:351
#, php-format
msgctxt "1. Site name, 2. Status"
msgid "\"%1$s\" site was %2$s"
msgstr ""

#: connectors/class-connector-buddypress.php:113
msgctxt "buddypress"
msgid "BuddyPress"
msgstr ""

#: connectors/class-connector-buddypress.php:123
msgctxt "buddypress"
msgid "Created"
msgstr ""

#: connectors/class-connector-buddypress.php:124
msgctxt "buddypress"
msgid "Updated"
msgstr ""

#: connectors/class-connector-buddypress.php:125
msgctxt "buddypress"
msgid "Activated"
msgstr ""

#: connectors/class-connector-buddypress.php:126
msgctxt "buddypress"
msgid "Deactivated"
msgstr ""

#: connectors/class-connector-buddypress.php:127
msgctxt "buddypress"
msgid "Deleted"
msgstr ""

#: connectors/class-connector-buddypress.php:128
msgctxt "buddypress"
msgid "Marked as spam"
msgstr ""

#: connectors/class-connector-buddypress.php:129
msgctxt "buddypress"
msgid "Unmarked as spam"
msgstr ""

#: connectors/class-connector-buddypress.php:130
msgctxt "buddypress"
msgid "Promoted"
msgstr ""

#: connectors/class-connector-buddypress.php:131
msgctxt "buddypress"
msgid "Demoted"
msgstr ""

#: connectors/class-connector-buddypress.php:142
msgctxt "buddypress"
msgid "Components"
msgstr ""

#: connectors/class-connector-buddypress.php:143
msgctxt "buddypress"
msgid "Groups"
msgstr ""

#: connectors/class-connector-buddypress.php:144
msgctxt "buddypress"
msgid "Activity"
msgstr ""

#: connectors/class-connector-buddypress.php:145
msgctxt "buddypress"
msgid "Profile fields"
msgstr ""

#: connectors/class-connector-buddypress.php:173
#: connectors/class-connector-buddypress.php:186
msgid "Edit setting"
msgstr ""

#: connectors/class-connector-buddypress.php:181
msgid "Edit Page"
msgstr ""

#: connectors/class-connector-buddypress.php:182
#: connectors/class-connector-gravityforms.php:145
#: connectors/class-connector-gravityforms.php:155
#: connectors/class-connector-media.php:111
#: connectors/class-connector-posts.php:107
#: connectors/class-connector-taxonomies.php:106
#: connectors/class-connector-woocommerce.php:220
#: connectors/class-connector-wordpress-seo.php:165
msgid "View"
msgstr ""

#: connectors/class-connector-buddypress.php:203
msgid "Edit group"
msgstr ""

#: connectors/class-connector-buddypress.php:204
msgid "View group"
msgstr ""

#: connectors/class-connector-buddypress.php:205
msgid "Delete group"
msgstr ""

#: connectors/class-connector-buddypress.php:221
msgid "Ham"
msgstr ""

#: connectors/class-connector-buddypress.php:224
msgid "Spam"
msgstr ""

#: connectors/class-connector-buddypress.php:226
#: connectors/class-connector-buddypress.php:241
#: connectors/class-connector-buddypress.php:263
msgid "Delete"
msgstr ""

#: connectors/class-connector-buddypress.php:284
#: connectors/class-connector-buddypress.php:288
msgctxt "buddypress"
msgid "Toolbar"
msgstr ""

#: connectors/class-connector-buddypress.php:292
msgctxt "buddypress"
msgid "Account Deletion"
msgstr ""

#: connectors/class-connector-buddypress.php:296
msgctxt "buddypress"
msgid "Profile Syncing"
msgstr ""

#: connectors/class-connector-buddypress.php:300
msgctxt "buddypress"
msgid "Group Creation"
msgstr ""

#: connectors/class-connector-buddypress.php:304
msgctxt "buddypress"
msgid "bbPress Configuration"
msgstr ""

#: connectors/class-connector-buddypress.php:308
msgctxt "buddypress"
msgid "Blog &amp; Forum Comments"
msgstr ""

#: connectors/class-connector-buddypress.php:312
msgctxt "buddypress"
msgid "Activity auto-refresh"
msgstr ""

#: connectors/class-connector-buddypress.php:316
msgctxt "buddypress"
msgid "Akismet"
msgstr ""

#: connectors/class-connector-buddypress.php:320
msgctxt "buddypress"
msgid "Avatar Uploads"
msgstr ""

#: connectors/class-connector-buddypress.php:367
#: connectors/class-connector-edd.php:248
#: connectors/class-connector-edd.php:293
#: connectors/class-connector-gravityforms.php:481
#: connectors/class-connector-jetpack.php:502
#: connectors/class-connector-jetpack.php:677
#, php-format
msgid "\"%s\" setting updated"
msgstr ""

#: connectors/class-connector-buddypress.php:390
#: connectors/class-connector-edd.php:365
#: connectors/class-connector-gravityforms.php:353
#: connectors/class-connector-gravityforms.php:399
#: connectors/class-connector-gravityforms.php:649
#: connectors/class-connector-jetpack.php:341
#: connectors/class-connector-jetpack.php:443
msgid "activated"
msgstr ""

#: connectors/class-connector-buddypress.php:391
#: connectors/class-connector-edd.php:365
#: connectors/class-connector-gravityforms.php:353
#: connectors/class-connector-gravityforms.php:399
#: connectors/class-connector-jetpack.php:341
#: connectors/class-connector-jetpack.php:443
msgid "deactivated"
msgstr ""

#: connectors/class-connector-buddypress.php:401
#, php-format
msgid "\"%1$s\" component %2$s"
msgstr ""

#: connectors/class-connector-buddypress.php:432
msgctxt "buddypress"
msgid "Register"
msgstr ""

#: connectors/class-connector-buddypress.php:433
msgctxt "buddypress"
msgid "Activate"
msgstr ""

#: connectors/class-connector-buddypress.php:442
msgid "No page"
msgstr ""

#: connectors/class-connector-buddypress.php:446
#, php-format
msgid "\"%1$s\" page set to \"%2$s\""
msgstr ""

#: connectors/class-connector-buddypress.php:480
#, php-format
msgid "\"%s\" activity deleted"
msgstr ""

#: connectors/class-connector-buddypress.php:502
#, php-format
msgid "\"%s\" activities were deleted"
msgstr ""

#: connectors/class-connector-buddypress.php:522
#, php-format
msgid "Marked activity \"%s\" as spam"
msgstr ""

#: connectors/class-connector-buddypress.php:542
#, php-format
msgid "Unmarked activity \"%s\" as spam"
msgstr ""

#: connectors/class-connector-buddypress.php:562
#, php-format
msgid "\"%s\" activity updated"
msgstr ""

#: connectors/class-connector-buddypress.php:588
#, php-format
msgid "\"%s\" group created"
msgstr ""

#: connectors/class-connector-buddypress.php:590
#, php-format
msgid "\"%s\" group updated"
msgstr ""

#: connectors/class-connector-buddypress.php:592
#, php-format
msgid "\"%s\" group deleted"
msgstr ""

#: connectors/class-connector-buddypress.php:594
#, php-format
msgid "Joined group \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:596
#, php-format
msgid "Left group \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:598
#, php-format
msgid "Banned \"%2$s\" from \"%1$s\""
msgstr ""

#: connectors/class-connector-buddypress.php:601
#, php-format
msgid "Unbanned \"%2$s\" from \"%1$s\""
msgstr ""

#: connectors/class-connector-buddypress.php:604
#, php-format
msgid "Removed \"%2$s\" from \"%1$s\""
msgstr ""

#: connectors/class-connector-buddypress.php:672
msgctxt "buddypress"
msgid "Administrator"
msgstr ""

#: connectors/class-connector-buddypress.php:673
msgctxt "buddypress"
msgid "Moderator"
msgstr ""

#: connectors/class-connector-buddypress.php:676
#, php-format
msgid "Promoted \"%s\" to \"%s\" in \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:688
#, php-format
msgid "Demoted \"%s\" to \"%s\" in \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:690
msgctxt "buddypress"
msgid "Member"
msgstr ""

#: connectors/class-connector-buddypress.php:715
#, php-format
msgid "Created profile field \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:717
#, php-format
msgid "Updated profile field \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:719
#, php-format
msgid "Deleted profile field \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:760
#, php-format
msgid "Created profile field group \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:762
#, php-format
msgid "Updated profile field group \"%s\""
msgstr ""

#: connectors/class-connector-buddypress.php:764
#, php-format
msgid "Deleted profile field group \"%s\""
msgstr ""

#: connectors/class-connector-comments.php:45
#: connectors/class-connector-comments.php:77
msgid "Comments"
msgstr ""

#: connectors/class-connector-comments.php:56
msgid "Edited"
msgstr ""

#: connectors/class-connector-comments.php:57
msgid "Replied"
msgstr ""

#: connectors/class-connector-comments.php:58
msgid "Approved"
msgstr ""

#: connectors/class-connector-comments.php:59
msgid "Unapproved"
msgstr ""

#: connectors/class-connector-comments.php:60
#: connectors/class-connector-gravityforms.php:424
#: connectors/class-connector-posts.php:40
msgid "Trashed"
msgstr ""

#: connectors/class-connector-comments.php:61
#: connectors/class-connector-gravityforms.php:425
#: connectors/class-connector-posts.php:41
msgid "Restored"
msgstr ""

#: connectors/class-connector-comments.php:62
msgid "Marked as Spam"
msgstr ""

#: connectors/class-connector-comments.php:63
msgid "Unmarked as Spam"
msgstr ""

#: connectors/class-connector-comments.php:65
msgid "Duplicate"
msgstr ""

#: connectors/class-connector-comments.php:66
msgid "Throttled"
msgstr ""

#: connectors/class-connector-comments.php:90
msgid "Comment"
msgstr ""

#: connectors/class-connector-comments.php:91
msgid "Trackback"
msgstr ""

#: connectors/class-connector-comments.php:92
msgid "Pingback"
msgstr ""

#: connectors/class-connector-comments.php:136
msgid "Unapprove"
msgstr ""

#: connectors/class-connector-comments.php:144
msgid "Approve"
msgstr ""

#: connectors/class-connector-comments.php:177
msgid "Guest"
msgstr ""

#: connectors/class-connector-comments.php:225
msgid "a logged out user"
msgstr ""

#: connectors/class-connector-comments.php:229
#, php-format
msgid "Comment flooding by %s detected and prevented"
msgstr ""

#: connectors/class-connector-comments.php:254
#: connectors/class-connector-comments.php:322
#: connectors/class-connector-comments.php:386
#: connectors/class-connector-comments.php:424
#: connectors/class-connector-comments.php:458
#: connectors/class-connector-comments.php:492
#: connectors/class-connector-comments.php:526
#: connectors/class-connector-comments.php:564
#: connectors/class-connector-comments.php:602
msgid "a post"
msgstr ""

#: connectors/class-connector-comments.php:255
msgid "approved automatically"
msgstr ""

#: connectors/class-connector-comments.php:255
msgid "pending approval"
msgstr ""

#: connectors/class-connector-comments.php:266
msgid "automatically marked as spam by Akismet"
msgstr ""

#: connectors/class-connector-comments.php:278
#, php-format
msgctxt ""
"1: Parent comment's author, 2: Comment author, 3: Post title, 4: Comment "
"status, 5: Comment type"
msgid "Reply to %1$s's %5$s by %2$s on %3$s %4$s"
msgstr ""

#: connectors/class-connector-comments.php:291
#, php-format
msgctxt "1: Comment author, 2: Post title 3: Comment status, 4: Comment type"
msgid "New %4$s by %1$s on %2$s %3$s"
msgstr ""

#: connectors/class-connector-comments.php:327
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s on %2$s edited"
msgstr ""

#: connectors/class-connector-comments.php:395
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s on %2$s deleted permanently"
msgstr ""

#: connectors/class-connector-comments.php:429
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s on %2$s trashed"
msgstr ""

#: connectors/class-connector-comments.php:463
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s on %2$s restored"
msgstr ""

#: connectors/class-connector-comments.php:497
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s on %2$s marked as spam"
msgstr ""

#: connectors/class-connector-comments.php:531
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s on %2$s unmarked as spam"
msgstr ""

#: connectors/class-connector-comments.php:569
#, php-format
msgctxt ""
"Comment status transition. 1: Comment author, 2: Post title, 3: Comment type"
msgid "%1$s's %3$s %2$s"
msgstr ""

#: connectors/class-connector-comments.php:607
#, php-format
msgctxt "1: Comment author, 2: Post title, 3: Comment type"
msgid "Duplicate %3$s by %1$s prevented on %2$s"
msgstr ""

#: connectors/class-connector-edd.php:99
msgctxt "edd"
msgid "Easy Digital Downloads"
msgstr ""

#: connectors/class-connector-edd.php:109
msgctxt "edd"
msgid "Created"
msgstr ""

#: connectors/class-connector-edd.php:110
msgctxt "edd"
msgid "Updated"
msgstr ""

#: connectors/class-connector-edd.php:111
msgctxt "edd"
msgid "Added"
msgstr ""

#: connectors/class-connector-edd.php:112
msgctxt "edd"
msgid "Deleted"
msgstr ""

#: connectors/class-connector-edd.php:113
msgctxt "edd"
msgid "Trashed"
msgstr ""

#: connectors/class-connector-edd.php:114
msgctxt "edd"
msgid "Restored"
msgstr ""

#: connectors/class-connector-edd.php:115
msgctxt "edd"
msgid "Generated"
msgstr ""

#: connectors/class-connector-edd.php:116
msgctxt "edd"
msgid "Imported"
msgstr ""

#: connectors/class-connector-edd.php:117
msgctxt "edd"
msgid "Exported"
msgstr ""

#: connectors/class-connector-edd.php:118
msgctxt "edd"
msgid "Revoked"
msgstr ""

#: connectors/class-connector-edd.php:129
msgctxt "edd"
msgid "Downloads"
msgstr ""

#: connectors/class-connector-edd.php:130
msgctxt "edd"
msgid "Categories"
msgstr ""

#: connectors/class-connector-edd.php:131
msgctxt "edd"
msgid "Tags"
msgstr ""

#: connectors/class-connector-edd.php:132
msgctxt "edd"
msgid "Discounts"
msgstr ""

#: connectors/class-connector-edd.php:133
msgctxt "edd"
msgid "Reports"
msgstr ""

#: connectors/class-connector-edd.php:134
msgctxt "edd"
msgid "API Keys"
msgstr ""

#: connectors/class-connector-edd.php:157
#: connectors/class-connector-edd.php:184
#, php-format
msgid "Edit %s"
msgstr ""

#: connectors/class-connector-edd.php:166
#, php-format
msgid "Deactivate %s"
msgstr ""

#: connectors/class-connector-edd.php:174
#, php-format
msgid "Activate %s"
msgstr ""

#: connectors/class-connector-edd.php:189
msgid "View API Log"
msgstr ""

#: connectors/class-connector-edd.php:192
msgid "Revoke"
msgstr ""

#: connectors/class-connector-edd.php:193
msgid "Reissue"
msgstr ""

#: connectors/class-connector-edd.php:276
msgctxt "edd"
msgid "Banned emails"
msgstr ""

#: connectors/class-connector-edd.php:331
#, php-format
msgid "\"%1s\" discount deleted"
msgstr ""

#: connectors/class-connector-edd.php:363
#, php-format
msgid "\"%1$s\" discount %2$s"
msgstr ""

#: connectors/class-connector-edd.php:395
msgid "Sales and Earnings"
msgstr ""

#: connectors/class-connector-edd.php:397
msgid "Earnings"
msgstr ""

#: connectors/class-connector-edd.php:399
msgid "Payments"
msgstr ""

#: connectors/class-connector-edd.php:401
msgid "Emails"
msgstr ""

#: connectors/class-connector-edd.php:403
msgid "Download History"
msgstr ""

#: connectors/class-connector-edd.php:408
#, php-format
msgid "Generated %s report"
msgstr ""

#: connectors/class-connector-edd.php:422
msgid "Exported Settings"
msgstr ""

#: connectors/class-connector-edd.php:432
msgid "Imported Settings"
msgstr ""

#: connectors/class-connector-edd.php:470
msgid "revoked"
msgstr ""

#: connectors/class-connector-edd.php:473
#: connectors/class-connector-gravityforms.php:216
#: connectors/class-connector-gravityforms.php:247
#: connectors/class-connector-gravityforms.php:280
msgid "created"
msgstr ""

#: connectors/class-connector-edd.php:476
#: connectors/class-connector-gravityforms.php:216
#: connectors/class-connector-gravityforms.php:247
#: connectors/class-connector-gravityforms.php:280
#: connectors/class-connector-gravityforms.php:497
msgid "updated"
msgstr ""

#: connectors/class-connector-edd.php:481
#, php-format
msgid "User API Key %s"
msgstr ""

#: connectors/class-connector-editor.php:44
msgid "Editor"
msgstr ""

#: connectors/class-connector-editor.php:72
#: connectors/class-connector-installer.php:61
msgid "Themes"
msgstr ""

#: connectors/class-connector-editor.php:73
#: connectors/class-connector-installer.php:60
msgid "Plugins"
msgstr ""

#: connectors/class-connector-editor.php:112
#, php-format
msgctxt "1: File name, 2: Theme/plugin name"
msgid "\"%1$s\" in \"%2$s\" updated"
msgstr ""

#: connectors/class-connector-editor.php:140
#: connectors/class-connector-editor.php:157
msgid "Edit File"
msgstr ""

#: connectors/class-connector-editor.php:148
msgid "Theme Details"
msgstr ""

#: connectors/class-connector-gravityforms.php:88
msgctxt "gravityforms"
msgid "Gravity Forms"
msgstr ""

#: connectors/class-connector-gravityforms.php:98
msgctxt "gravityforms"
msgid "Created"
msgstr ""

#: connectors/class-connector-gravityforms.php:99
msgctxt "gravityforms"
msgid "Updated"
msgstr ""

#: connectors/class-connector-gravityforms.php:100
msgctxt "gravityforms"
msgid "Exported"
msgstr ""

#: connectors/class-connector-gravityforms.php:101
msgctxt "gravityforms"
msgid "Imported"
msgstr ""

#: connectors/class-connector-gravityforms.php:102
msgctxt "gravityforms"
msgid "Added"
msgstr ""

#: connectors/class-connector-gravityforms.php:103
msgctxt "gravityforms"
msgid "Deleted"
msgstr ""

#: connectors/class-connector-gravityforms.php:104
msgctxt "gravityforms"
msgid "Trashed"
msgstr ""

#: connectors/class-connector-gravityforms.php:105
msgctxt "gravityforms"
msgid "Restored"
msgstr ""

#: connectors/class-connector-gravityforms.php:106
msgctxt "gravityforms"
msgid "Duplicated"
msgstr ""

#: connectors/class-connector-gravityforms.php:117
msgctxt "gravityforms"
msgid "Forms"
msgstr ""

#: connectors/class-connector-gravityforms.php:118
msgctxt "gravityforms"
msgid "Settings"
msgstr ""

#: connectors/class-connector-gravityforms.php:119
msgctxt "gravityforms"
msgid "Import/Export"
msgstr ""

#: connectors/class-connector-gravityforms.php:120
msgctxt "gravityforms"
msgid "Entries"
msgstr ""

#: connectors/class-connector-gravityforms.php:121
msgctxt "gravityforms"
msgid "Notes"
msgstr ""

#: connectors/class-connector-gravityforms.php:165
msgid "Edit Settings"
msgstr ""

#: connectors/class-connector-gravityforms.php:181
msgctxt "gravityforms"
msgid "Output CSS"
msgstr ""

#: connectors/class-connector-gravityforms.php:184
msgctxt "gravityforms"
msgid "Output HTML5"
msgstr ""

#: connectors/class-connector-gravityforms.php:187
msgctxt "gravityforms"
msgid "No-Conflict Mode"
msgstr ""

#: connectors/class-connector-gravityforms.php:190
msgctxt "gravityforms"
msgid "Currency"
msgstr ""

#: connectors/class-connector-gravityforms.php:193
msgctxt "gravityforms"
msgid "reCAPTCHA Public Key"
msgstr ""

#: connectors/class-connector-gravityforms.php:196
msgctxt "gravityforms"
msgid "reCAPTCHA Private Key"
msgstr ""

#: connectors/class-connector-gravityforms.php:214
#: connectors/class-connector-gravityforms.php:430
#, php-format
msgid "\"%1$s\" form %2$s"
msgstr ""

#: connectors/class-connector-gravityforms.php:245
#, php-format
msgid "\"%1$s\" confirmation %2$s for \"%3$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:278
#, php-format
msgid "\"%1$s\" notification %2$s for \"%3$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:304
#, php-format
msgid "\"%1$s\" notification deleted from \"%2$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:327
#, php-format
msgid "\"%1$s\" confirmation deleted from \"%2$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:351
#, php-format
msgid "\"%1$s\" confirmation %2$s from \"%3$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:376
#, php-format
msgid "\"%s\" form views reset"
msgstr ""

#: connectors/class-connector-gravityforms.php:397
#, php-format
msgid "\"%1$s\" notification %2$s from \"%3$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:422
#: connectors/class-connector-installer.php:46
msgid "Activated"
msgstr ""

#: connectors/class-connector-gravityforms.php:423
#: connectors/class-connector-installer.php:47
#: connectors/class-connector-widgets.php:61
msgid "Deactivated"
msgstr ""

#: connectors/class-connector-gravityforms.php:496
#, php-format
msgid "Gravity Forms license key %s"
msgstr ""

#: connectors/class-connector-gravityforms.php:510
#, php-format
msgid "\"%s\" form exported"
msgstr ""

#: connectors/class-connector-gravityforms.php:525
msgid "Import process started"
msgstr ""

#: connectors/class-connector-gravityforms.php:540
#, php-format
msgid "Export process started for %d forms"
msgstr ""

#: connectors/class-connector-gravityforms.php:558
#, php-format
msgid "\"%s\" form deleted"
msgstr ""

#: connectors/class-connector-gravityforms.php:574
#, php-format
msgid "\"%1$s\" form created as duplicate from \"%2$s\""
msgstr ""

#: connectors/class-connector-gravityforms.php:592
#, php-format
msgid "Lead #%1$d from \"%2$s\" deleted"
msgstr ""

#: connectors/class-connector-gravityforms.php:609
#, php-format
msgid "Note #%1$d added to lead #%2$d on \"%3$s\" form"
msgstr ""

#: connectors/class-connector-gravityforms.php:627
#, php-format
msgid "Note #%1$d deleted from lead #%2$d on \"%3$s\" form"
msgstr ""

#: connectors/class-connector-gravityforms.php:651
msgid "trashed"
msgstr ""

#: connectors/class-connector-gravityforms.php:661
#: connectors/class-connector-gravityforms.php:708
#, php-format
msgid "Lead #%1$d %2$s on \"%3$s\" form"
msgstr ""

#: connectors/class-connector-gravityforms.php:685
#, php-format
msgid "Lead #%1$d marked as %2$s on \"%3$s\" form"
msgstr ""

#: connectors/class-connector-gravityforms.php:687
msgid "read"
msgstr ""

#: connectors/class-connector-gravityforms.php:687
msgid "unread"
msgstr ""

#: connectors/class-connector-gravityforms.php:710
msgid "starred"
msgstr ""

#: connectors/class-connector-gravityforms.php:710
msgid "unstarred"
msgstr ""

#: connectors/class-connector-installer.php:35
msgid "Installer"
msgstr ""

#: connectors/class-connector-installer.php:45
msgid "Installed"
msgstr ""

#: connectors/class-connector-installer.php:62
msgid "WordPress"
msgstr ""

#: connectors/class-connector-installer.php:83
msgid "About"
msgstr ""

#: connectors/class-connector-installer.php:86
msgid "View Release Notes"
msgstr ""

#: connectors/class-connector-installer.php:166
#, php-format
msgctxt ""
"Plugin/theme installation. 1: Type (plugin/theme), 2: Plugin/theme name, 3: "
"Plugin/theme version"
msgid "Installed %1$s: %2$s %3$s"
msgstr ""

#: connectors/class-connector-installer.php:175
#, php-format
msgctxt ""
"Plugin/theme update. 1: Type (plugin/theme), 2: Plugin/theme name, 3: Plugin/"
"theme version"
msgid "Updated %1$s: %2$s %3$s"
msgstr ""

#: connectors/class-connector-installer.php:244
#: connectors/class-connector-installer.php:262
#: connectors/class-connector-installer.php:367
msgid "network wide"
msgstr ""

#: connectors/class-connector-installer.php:248
#, php-format
msgctxt "1: Plugin name, 2: Single site or network wide"
msgid "\"%1$s\" plugin activated %2$s"
msgstr ""

#: connectors/class-connector-installer.php:266
#, php-format
msgctxt "1: Plugin name, 2: Single site or network wide"
msgid "\"%1$s\" plugin deactivated %2$s"
msgstr ""

#: connectors/class-connector-installer.php:280
#, php-format
msgid "\"%s\" theme activated"
msgstr ""

#: connectors/class-connector-installer.php:310
#, php-format
msgid "\"%s\" theme deleted"
msgstr ""

#: connectors/class-connector-installer.php:370
#, php-format
msgid "\"%s\" plugin deleted"
msgstr ""

#: connectors/class-connector-installer.php:390
#, php-format
msgid "WordPress auto-updated to %s"
msgstr ""

#: connectors/class-connector-installer.php:392
#, php-format
msgid "WordPress updated to %s"
msgstr ""

#: connectors/class-connector-jetpack.php:69
msgctxt "jetpack"
msgid "Jetpack"
msgstr ""

#: connectors/class-connector-jetpack.php:79
msgctxt "jetpack"
msgid "Activated"
msgstr ""

#: connectors/class-connector-jetpack.php:80
msgctxt "jetpack"
msgid "Dectivated"
msgstr ""

#: connectors/class-connector-jetpack.php:81
msgctxt "jetpack"
msgid "Connected"
msgstr ""

#: connectors/class-connector-jetpack.php:82
msgctxt "jetpack"
msgid "Disconnected"
msgstr ""

#: connectors/class-connector-jetpack.php:83
msgctxt "jetpack"
msgid "Link"
msgstr ""

#: connectors/class-connector-jetpack.php:84
msgctxt "jetpack"
msgid "Unlink"
msgstr ""

#: connectors/class-connector-jetpack.php:85
msgctxt "jetpack"
msgid "Updated"
msgstr ""

#: connectors/class-connector-jetpack.php:86
msgctxt "jetpack"
msgid "Added"
msgstr ""

#: connectors/class-connector-jetpack.php:87
msgctxt "jetpack"
msgid "Removed"
msgstr ""

#: connectors/class-connector-jetpack.php:98
msgctxt "jetpack"
msgid "Blogs"
msgstr ""

#: connectors/class-connector-jetpack.php:99
msgctxt "jetpack"
msgid "Carousel"
msgstr ""

#: connectors/class-connector-jetpack.php:100
msgctxt "jetpack"
msgid "Custom CSS"
msgstr ""

#: connectors/class-connector-jetpack.php:101
msgctxt "jetpack"
msgid "Google+ Profile"
msgstr ""

#: connectors/class-connector-jetpack.php:102
msgctxt "jetpack"
msgid "Infinite Scroll"
msgstr ""

#: connectors/class-connector-jetpack.php:103
msgctxt "jetpack"
msgid "Comments"
msgstr ""

#: connectors/class-connector-jetpack.php:104
msgctxt "jetpack"
msgid "Likes"
msgstr ""

#: connectors/class-connector-jetpack.php:105
msgctxt "jetpack"
msgid "Mobile"
msgstr ""

#: connectors/class-connector-jetpack.php:106
msgctxt "jetpack"
msgid "Modules"
msgstr ""

#: connectors/class-connector-jetpack.php:107
msgctxt "jetpack"
msgid "Monitor"
msgstr ""

#: connectors/class-connector-jetpack.php:108
msgctxt "jetpack"
msgid "Options"
msgstr ""

#: connectors/class-connector-jetpack.php:109
msgctxt "jetpack"
msgid "Post by Email"
msgstr ""

#: connectors/class-connector-jetpack.php:110
msgctxt "jetpack"
msgid "Protect"
msgstr ""

#: connectors/class-connector-jetpack.php:111
msgctxt "jetpack"
msgid "Publicize"
msgstr ""

#: connectors/class-connector-jetpack.php:112
msgctxt "jetpack"
msgid "Related Posts"
msgstr ""

#: connectors/class-connector-jetpack.php:113
msgctxt "jetpack"
msgid "Sharing"
msgstr ""

#: connectors/class-connector-jetpack.php:114
msgctxt "jetpack"
msgid "Subscriptions"
msgstr ""

#: connectors/class-connector-jetpack.php:115
msgctxt "jetpack"
msgid "SSO"
msgstr ""

#: connectors/class-connector-jetpack.php:116
msgctxt "jetpack"
msgid "WordPress.com Stats"
msgstr ""

#: connectors/class-connector-jetpack.php:117
msgctxt "jetpack"
msgid "Tiled Galleries"
msgstr ""

#: connectors/class-connector-jetpack.php:118
msgctxt "jetpack"
msgid "Users"
msgstr ""

#: connectors/class-connector-jetpack.php:119
msgctxt "jetpack"
msgid "Site Verification"
msgstr ""

#: connectors/class-connector-jetpack.php:120
msgctxt "jetpack"
msgid "VideoPress"
msgstr ""

#: connectors/class-connector-jetpack.php:146
msgid "Configure"
msgstr ""

#: connectors/class-connector-jetpack.php:149
msgid "Deactivate"
msgstr ""

#: connectors/class-connector-jetpack.php:160
msgid "Activate"
msgstr ""

#: connectors/class-connector-jetpack.php:175
msgid "Configure module"
msgstr ""

#: connectors/class-connector-jetpack.php:194
msgid "Sharing options"
msgstr ""

#: connectors/class-connector-jetpack.php:199
msgid "Twitter site tag"
msgstr ""

#: connectors/class-connector-jetpack.php:204
msgid "WordPress.com Stats"
msgstr ""

#: connectors/class-connector-jetpack.php:209
msgid "Color Scheme"
msgstr ""

#: connectors/class-connector-jetpack.php:214
msgid "WP.com Site-wide Likes"
msgstr ""

#: connectors/class-connector-jetpack.php:219
msgid "Excerpts appearance"
msgstr ""

#: connectors/class-connector-jetpack.php:223
msgid "App promos"
msgstr ""

#: connectors/class-connector-jetpack.php:231
msgid "Background color"
msgstr ""

#: connectors/class-connector-jetpack.php:235
msgid "Metadata"
msgstr ""

#: connectors/class-connector-jetpack.php:240
msgid "Follow blog comment form button"
msgstr ""

#: connectors/class-connector-jetpack.php:244
msgid "Follow comments form button"
msgstr ""

#: connectors/class-connector-jetpack.php:249
msgid "Greeting Text"
msgstr ""

#: connectors/class-connector-jetpack.php:254
msgid "Infinite Scroll Google Analytics"
msgstr ""

#: connectors/class-connector-jetpack.php:259
msgid "Blocked Attempts"
msgstr ""

#: connectors/class-connector-jetpack.php:264
msgid "Require Two-Step Authentication"
msgstr ""

#: connectors/class-connector-jetpack.php:268
msgid "Match by Email"
msgstr ""

#: connectors/class-connector-jetpack.php:274
msgid "Show Related Posts Headline"
msgstr ""

#: connectors/class-connector-jetpack.php:278
msgid "Show Related Posts Thumbnails"
msgstr ""

#: connectors/class-connector-jetpack.php:285
msgid "Google Webmaster Tools Token"
msgstr ""

#: connectors/class-connector-jetpack.php:289
msgid "Bing Webmaster Center Token"
msgstr ""

#: connectors/class-connector-jetpack.php:293
msgid "Pinterest Site Verification Token"
msgstr ""

#: connectors/class-connector-jetpack.php:299
msgid "Tiled Galleries"
msgstr ""

#: connectors/class-connector-jetpack.php:339
#, php-format
msgid "%1$s module %2$s"
msgstr ""

#: connectors/class-connector-jetpack.php:357
#, php-format
msgid "%1$s's account %2$s %3$s Jetpack"
msgstr ""

#: connectors/class-connector-jetpack.php:359
msgid "unlinked"
msgstr ""

#: connectors/class-connector-jetpack.php:359
msgid "linked"
msgstr ""

#: connectors/class-connector-jetpack.php:360
msgid "from"
msgstr ""

#: connectors/class-connector-jetpack.php:360
msgid "to"
msgstr ""

#: connectors/class-connector-jetpack.php:374
#, php-format
msgid "Site %s Jetpack"
msgstr ""

#: connectors/class-connector-jetpack.php:375
#: connectors/class-connector-jetpack.php:385
msgid "connected to"
msgstr ""

#: connectors/class-connector-jetpack.php:375
#: connectors/class-connector-jetpack.php:385
msgid "disconnected from"
msgstr ""

#: connectors/class-connector-jetpack.php:383
#, php-format
msgid "\"%1$s\" blog %2$s Jetpack"
msgstr ""

#: connectors/class-connector-jetpack.php:410
msgid "Sharing services updated"
msgstr ""

#: connectors/class-connector-jetpack.php:441
#, php-format
msgid "Monitor notifications %s"
msgstr ""

#: connectors/class-connector-jetpack.php:468
#: connectors/class-connector-jetpack.php:559
msgid "enabled"
msgstr ""

#: connectors/class-connector-jetpack.php:470
#: connectors/class-connector-jetpack.php:559
msgid "disabled"
msgstr ""

#: connectors/class-connector-jetpack.php:472
msgid "regenerated"
msgstr ""

#: connectors/class-connector-jetpack.php:478
#, php-format
msgid "%1$s %2$s Post by Email"
msgstr ""

#: connectors/class-connector-jetpack.php:557
#, php-format
msgid "G+ profile display %s"
msgstr ""

#: connectors/class-connector-jetpack.php:574
#, php-format
msgid "%1$s's Google+ account %2$s"
msgstr ""

#: connectors/class-connector-jetpack.php:577
msgid "connected"
msgstr ""

#: connectors/class-connector-jetpack.php:577
msgid "disconnected"
msgstr ""

#: connectors/class-connector-jetpack.php:594
#, php-format
msgid "Sharing CSS/JS %s"
msgstr ""

#: connectors/class-connector-jetpack.php:633
msgid "Custom CSS updated"
msgstr ""

#: connectors/class-connector-jetpack.php:653
#, php-format
msgid "%1$s connection %2$s"
msgstr ""

#: connectors/class-connector-jetpack.php:656
msgid "added"
msgstr ""

#: connectors/class-connector-jetpack.php:656
msgid "removed"
msgstr ""

#: connectors/class-connector-jetpack.php:666
msgid "Video Library Access"
msgstr ""

#: connectors/class-connector-jetpack.php:667
msgid "Allow users to upload videos"
msgstr ""

#: connectors/class-connector-jetpack.php:668
msgid "Free formats"
msgstr ""

#: connectors/class-connector-jetpack.php:669
msgid "Default quality"
msgstr ""

#: connectors/class-connector-media.php:31
#: connectors/class-connector-settings.php:131
msgid "Media"
msgstr ""

#: connectors/class-connector-media.php:41
msgid "Attached"
msgstr ""

#: connectors/class-connector-media.php:42
msgid "Uploaded"
msgstr ""

#: connectors/class-connector-media.php:45
#: connectors/class-connector-menus.php:42
msgid "Assigned"
msgstr ""

#: connectors/class-connector-media.php:46
#: connectors/class-connector-menus.php:43
msgid "Unassigned"
msgstr ""

#: connectors/class-connector-media.php:59
msgid "Image"
msgstr ""

#: connectors/class-connector-media.php:60
msgid "Audio"
msgstr ""

#: connectors/class-connector-media.php:61
msgid "Video"
msgstr ""

#: connectors/class-connector-media.php:62
msgid "Document"
msgstr ""

#: connectors/class-connector-media.php:63
msgid "Spreadsheet"
msgstr ""

#: connectors/class-connector-media.php:64
msgid "Interactive"
msgstr ""

#: connectors/class-connector-media.php:65
msgid "Text"
msgstr ""

#: connectors/class-connector-media.php:66
msgid "Archive"
msgstr ""

#: connectors/class-connector-media.php:67
msgid "Code"
msgstr ""

#: connectors/class-connector-media.php:108
msgid "Edit Media"
msgstr ""

#: connectors/class-connector-media.php:129
#, php-format
msgctxt "1: Attachment title, 2: Parent post title"
msgid "Attached \"%1$s\" to \"%2$s\""
msgstr ""

#: connectors/class-connector-media.php:134
#, php-format
msgid "Added \"%s\" to Media library"
msgstr ""

#: connectors/class-connector-media.php:162
#, php-format
msgid "Updated \"%s\""
msgstr ""

#: connectors/class-connector-media.php:186
#, php-format
msgid "Deleted \"%s\""
msgstr ""

#: connectors/class-connector-media.php:222
#, php-format
msgid "Edited image \"%s\""
msgstr ""

#: connectors/class-connector-menus.php:29
msgid "Menus"
msgstr ""

#: connectors/class-connector-menus.php:86
msgid "Edit Menu"
msgstr ""

#: connectors/class-connector-menus.php:105
#, php-format
msgid "Created new menu \"%s\""
msgstr ""

#: connectors/class-connector-menus.php:129
#, php-format
msgctxt "Menu name"
msgid "Updated menu \"%s\""
msgstr ""

#: connectors/class-connector-menus.php:153
#, php-format
msgctxt "Menu name"
msgid "Deleted \"%s\""
msgstr ""

#: connectors/class-connector-menus.php:201
#, php-format
msgctxt "1: Menu name, 2: Theme location"
msgid "\"%1$s\" has been unassigned from \"%2$s\""
msgstr ""

#: connectors/class-connector-menus.php:209
#, php-format
msgctxt "1: Menu name, 2: Theme location"
msgid "\"%1$s\" has been assigned to \"%2$s\""
msgstr ""

#: connectors/class-connector-posts.php:28
msgid "Posts"
msgstr ""

#: connectors/class-connector-posts.php:101
#: connectors/class-connector-wordpress-seo.php:159
#, php-format
msgctxt "Post type singular name"
msgid "Restore %s"
msgstr ""

#: connectors/class-connector-posts.php:102
#: connectors/class-connector-wordpress-seo.php:160
#, php-format
msgctxt "Post type singular name"
msgid "Delete %s Permenantly"
msgstr ""

#: connectors/class-connector-posts.php:104
#: connectors/class-connector-woocommerce.php:216
#: connectors/class-connector-wordpress-seo.php:162
#, php-format
msgctxt "Post type singular name"
msgid "Edit %s"
msgstr ""

#: connectors/class-connector-posts.php:114
#: connectors/class-connector-wordpress-seo.php:169
msgid "Revision"
msgstr ""

#: connectors/class-connector-posts.php:159
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s unpublished"
msgstr ""

#: connectors/class-connector-posts.php:165
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s restored from trash"
msgstr ""

#: connectors/class-connector-posts.php:172
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s draft saved"
msgstr ""

#: connectors/class-connector-posts.php:178
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s drafted"
msgstr ""

#: connectors/class-connector-posts.php:184
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s pending review"
msgstr ""

#: connectors/class-connector-posts.php:190
#, php-format
msgctxt "1: Post title, 2: Post type singular name, 3: Scheduled post date"
msgid "\"%1$s\" %2$s scheduled for %3$s"
msgstr ""

#: connectors/class-connector-posts.php:196
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" scheduled %2$s published"
msgstr ""

#: connectors/class-connector-posts.php:202
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s published"
msgstr ""

#: connectors/class-connector-posts.php:208
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s privately published"
msgstr ""

#: connectors/class-connector-posts.php:214
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s trashed"
msgstr ""

#: connectors/class-connector-posts.php:221
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s updated"
msgstr ""

#: connectors/class-connector-posts.php:298
#, php-format
msgctxt "1: Post title, 2: Post type singular name"
msgid "\"%1$s\" %2$s deleted from trash"
msgstr ""

#: connectors/class-connector-posts.php:336
msgid "Post"
msgstr ""

#: connectors/class-connector-settings.php:128
msgid "Writing"
msgstr ""

#: connectors/class-connector-settings.php:129
msgid "Reading"
msgstr ""

#: connectors/class-connector-settings.php:130
msgid "Discussion"
msgstr ""

#: connectors/class-connector-settings.php:132
msgid "Permalinks"
msgstr ""

#: connectors/class-connector-settings.php:133
msgid "Network"
msgstr ""

#: connectors/class-connector-settings.php:135
msgid "Custom Background"
msgstr ""

#: connectors/class-connector-settings.php:136
msgid "Custom Header"
msgstr ""

#: connectors/class-connector-settings.php:143
msgid "Stream Network"
msgstr ""

#: connectors/class-connector-settings.php:144
msgid "Stream Defaults"
msgstr ""

#: connectors/class-connector-settings.php:242
msgid "Site Title"
msgstr ""

#: connectors/class-connector-settings.php:243
msgid "Tagline"
msgstr ""

#: connectors/class-connector-settings.php:244
#: connectors/class-connector-settings.php:245
msgid "E-mail Address"
msgstr ""

#: connectors/class-connector-settings.php:246
msgid "WordPress Address (URL)"
msgstr ""

#: connectors/class-connector-settings.php:247
msgid "Site Address (URL)"
msgstr ""

#: connectors/class-connector-settings.php:248
msgid "Membership"
msgstr ""

#: connectors/class-connector-settings.php:249
msgid "New User Default Role"
msgstr ""

#: connectors/class-connector-settings.php:250
msgid "Timezone"
msgstr ""

#: connectors/class-connector-settings.php:251
msgid "Date Format"
msgstr ""

#: connectors/class-connector-settings.php:252
msgid "Time Format"
msgstr ""

#: connectors/class-connector-settings.php:253
msgid "Week Starts On"
msgstr ""

#: connectors/class-connector-settings.php:255
#: connectors/class-connector-settings.php:256
msgid "Formatting"
msgstr ""

#: connectors/class-connector-settings.php:257
msgid "Default Post Category"
msgstr ""

#: connectors/class-connector-settings.php:258
msgid "Default Post Format"
msgstr ""

#: connectors/class-connector-settings.php:259
msgid "Mail Server"
msgstr ""

#: connectors/class-connector-settings.php:260
msgid "Login Name"
msgstr ""

#: connectors/class-connector-settings.php:261
msgid "Password"
msgstr ""

#: connectors/class-connector-settings.php:262
msgid "Default Mail Category"
msgstr ""

#: connectors/class-connector-settings.php:263
msgid "Update Services"
msgstr ""

#: connectors/class-connector-settings.php:265
#: connectors/class-connector-settings.php:266
#: connectors/class-connector-settings.php:267
msgid "Front page displays"
msgstr ""

#: connectors/class-connector-settings.php:268
msgid "Blog pages show at most"
msgstr ""

#: connectors/class-connector-settings.php:269
msgid "Syndication feeds show the most recent"
msgstr ""

#: connectors/class-connector-settings.php:270
msgid "For each article in a feed, show"
msgstr ""

#: connectors/class-connector-settings.php:271
msgid "Search Engine Visibility"
msgstr ""

#: connectors/class-connector-settings.php:273
#: connectors/class-connector-settings.php:274
#: connectors/class-connector-settings.php:275
msgid "Default article settings"
msgstr ""

#: connectors/class-connector-settings.php:276
#: connectors/class-connector-settings.php:277
#: connectors/class-connector-settings.php:278
#: connectors/class-connector-settings.php:279
#: connectors/class-connector-settings.php:280
#: connectors/class-connector-settings.php:281
#: connectors/class-connector-settings.php:282
#: connectors/class-connector-settings.php:283
#: connectors/class-connector-settings.php:284
#: connectors/class-connector-settings.php:285
msgid "Other comment settings"
msgstr ""

#: connectors/class-connector-settings.php:286
#: connectors/class-connector-settings.php:287
msgid "E-mail me whenever"
msgstr ""

#: connectors/class-connector-settings.php:288
#: connectors/class-connector-settings.php:289
msgid "Before a comment appears"
msgstr ""

#: connectors/class-connector-settings.php:290
#: connectors/class-connector-settings.php:291
msgid "Comment Moderation"
msgstr ""

#: connectors/class-connector-settings.php:292
msgid "Comment Blacklist"
msgstr ""

#: connectors/class-connector-settings.php:293
msgid "Show Avatars"
msgstr ""

#: connectors/class-connector-settings.php:294
msgid "Maximum Rating"
msgstr ""

#: connectors/class-connector-settings.php:295
msgid "Default Avatar"
msgstr ""

#: connectors/class-connector-settings.php:297
#: connectors/class-connector-settings.php:298
#: connectors/class-connector-settings.php:299
msgid "Thumbnail size"
msgstr ""

#: connectors/class-connector-settings.php:300
#: connectors/class-connector-settings.php:301
msgid "Medium size"
msgstr ""

#: connectors/class-connector-settings.php:302
#: connectors/class-connector-settings.php:303
msgid "Large size"
msgstr ""

#: connectors/class-connector-settings.php:304
msgid "Uploading Files"
msgstr ""

#: connectors/class-connector-settings.php:306
msgid "Permalink Settings"
msgstr ""

#: connectors/class-connector-settings.php:307
msgid "Category base"
msgstr ""

#: connectors/class-connector-settings.php:308
msgid "Tag base"
msgstr ""

#: connectors/class-connector-settings.php:310
msgid "Registration notification"
msgstr ""

#: connectors/class-connector-settings.php:311
msgid "Allow new registrations"
msgstr ""

#: connectors/class-connector-settings.php:312
msgid "Add New Users"
msgstr ""

#: connectors/class-connector-settings.php:313
msgid "Enable administration menus"
msgstr ""

#: connectors/class-connector-settings.php:314
msgid "Site upload space check"
msgstr ""

#: connectors/class-connector-settings.php:315
msgid "Site upload space"
msgstr ""

#: connectors/class-connector-settings.php:316
msgid "Upload file types"
msgstr ""

#: connectors/class-connector-settings.php:317
msgid "Network Title"
msgstr ""

#: connectors/class-connector-settings.php:318
msgid "First Post"
msgstr ""

#: connectors/class-connector-settings.php:319
msgid "First Page"
msgstr ""

#: connectors/class-connector-settings.php:320
msgid "First Comment"
msgstr ""

#: connectors/class-connector-settings.php:321
msgid "First Comment URL"
msgstr ""

#: connectors/class-connector-settings.php:322
msgid "First Comment Author"
msgstr ""

#: connectors/class-connector-settings.php:323
msgid "Welcome Email"
msgstr ""

#: connectors/class-connector-settings.php:324
msgid "Welcome User Email"
msgstr ""

#: connectors/class-connector-settings.php:325
msgid "Max upload file size"
msgstr ""

#: connectors/class-connector-settings.php:326
msgid "Terms Enabled"
msgstr ""

#: connectors/class-connector-settings.php:327
msgid "Banned Names"
msgstr ""

#: connectors/class-connector-settings.php:328
msgid "Limited Email Registrations"
msgstr ""

#: connectors/class-connector-settings.php:329
msgid "Banned Email Domains"
msgstr ""

#: connectors/class-connector-settings.php:330
msgid "Network Language"
msgstr ""

#: connectors/class-connector-settings.php:331
msgid "Blog Count"
msgstr ""

#: connectors/class-connector-settings.php:332
msgid "User Count"
msgstr ""

#: connectors/class-connector-settings.php:334
msgid "Stream Database Version"
msgstr ""

#: connectors/class-connector-settings.php:339
#: connectors/class-connector-settings.php:340
msgid "Network Admin Email"
msgstr ""

#: connectors/class-connector-settings.php:372
msgid "Background Image"
msgstr ""

#: connectors/class-connector-settings.php:373
msgid "Background Position"
msgstr ""

#: connectors/class-connector-settings.php:374
msgid "Background Repeat"
msgstr ""

#: connectors/class-connector-settings.php:375
msgid "Background Attachment"
msgstr ""

#: connectors/class-connector-settings.php:376
msgid "Background Color"
msgstr ""

#: connectors/class-connector-settings.php:378
msgid "Header Image"
msgstr ""

#: connectors/class-connector-settings.php:379
msgid "Text Color"
msgstr ""

#: connectors/class-connector-settings.php:515
#, php-format
msgid "Edit %s Settings"
msgstr ""

#: connectors/class-connector-settings.php:665
#, php-format
msgid "\"%s\" setting was updated"
msgstr ""

#: connectors/class-connector-taxonomies.php:44
msgid "Taxonomies"
msgstr ""

#: connectors/class-connector-taxonomies.php:105
#, php-format
msgctxt "Term singular name"
msgid "Edit %s"
msgstr ""

#: connectors/class-connector-taxonomies.php:154
#, php-format
msgctxt "1: Term name, 2: Taxonomy singular label"
msgid "\"%1$s\" %2$s created"
msgstr ""

#: connectors/class-connector-taxonomies.php:186
#, php-format
msgctxt "1: Term name, 2: Taxonomy singular label"
msgid "\"%1$s\" %2$s deleted"
msgstr ""

#: connectors/class-connector-taxonomies.php:228
#, php-format
msgctxt "1: Term name, 2: Taxonomy singular label"
msgid "\"%1$s\" %2$s updated"
msgstr ""

#: connectors/class-connector-user-switching.php:44
msgctxt "user-switching"
msgid "User Switching"
msgstr ""

#: connectors/class-connector-user-switching.php:144
#, php-format
msgctxt "1: User display name, 2: User login"
msgid "Switched user to %1$s (%2$s)"
msgstr ""

#: connectors/class-connector-user-switching.php:174
#, php-format
msgctxt "1: User display name, 2: User login"
msgid "Switched back to %1$s (%2$s)"
msgstr ""

#: connectors/class-connector-user-switching.php:208
msgid "Switched off"
msgstr ""

#: connectors/class-connector-users.php:41
#: connectors/class-connector-users.php:71
msgid "Users"
msgstr ""

#: connectors/class-connector-users.php:54
msgid "Password Reset"
msgstr ""

#: connectors/class-connector-users.php:55
msgid "Lost Password"
msgstr ""

#: connectors/class-connector-users.php:56
msgid "Log In"
msgstr ""

#: connectors/class-connector-users.php:57
msgid "Log Out"
msgstr ""

#: connectors/class-connector-users.php:58
msgid "Switched To"
msgstr ""

#: connectors/class-connector-users.php:59
msgid "Switched Back"
msgstr ""

#: connectors/class-connector-users.php:60
msgid "Switched Off"
msgstr ""

#: connectors/class-connector-users.php:72
msgid "Sessions"
msgstr ""

#: connectors/class-connector-users.php:73
msgid "Profiles"
msgstr ""

#: connectors/class-connector-users.php:90
msgid "Edit User"
msgstr ""

#: connectors/class-connector-users.php:139
msgid "New user registration"
msgstr ""

#: connectors/class-connector-users.php:143
#, php-format
msgctxt "1: User display name, 2: User role"
msgid "New user account created for %1$s (%2$s)"
msgstr ""

#: connectors/class-connector-users.php:175
#, php-format
msgid "%s's profile was updated"
msgstr ""

#: connectors/class-connector-users.php:203
#, php-format
msgctxt "1: User display name, 2: Old role, 3: New role"
msgid "%1$s's role was changed from %2$s to %3$s"
msgstr ""

#: connectors/class-connector-users.php:227
#, php-format
msgid "%s's password was reset"
msgstr ""

#: connectors/class-connector-users.php:253
#, php-format
msgid "%s's password was requested to be reset"
msgstr ""

#: connectors/class-connector-users.php:279
#, php-format
msgid "%s logged in"
msgstr ""

#: connectors/class-connector-users.php:302
#, php-format
msgid "%s logged out"
msgstr ""

#: connectors/class-connector-users.php:338
#, php-format
msgctxt "1: User display name, 2: User roles"
msgid "%1$s's account was deleted (%2$s)"
msgstr ""

#: connectors/class-connector-users.php:346
#, php-format
msgid "User account #%d was deleted"
msgstr ""

#: connectors/class-connector-widgets.php:46
msgid "Widgets"
msgstr ""

#: connectors/class-connector-widgets.php:57
msgid "Removed"
msgstr ""

#: connectors/class-connector-widgets.php:58
msgid "Moved"
msgstr ""

#: connectors/class-connector-widgets.php:62
msgid "Reactivated"
msgstr ""

#: connectors/class-connector-widgets.php:64
msgid "Sorted"
msgstr ""

#: connectors/class-connector-widgets.php:82
msgid "Inactive Widgets"
msgstr ""

#: connectors/class-connector-widgets.php:83
msgid "Orphaned Widgets"
msgstr ""

#: connectors/class-connector-widgets.php:103
msgid "Edit Widget Area"
msgstr ""

#: connectors/class-connector-widgets.php:199
#, php-format
msgctxt "1: Name, 2: Title, 3: Sidebar Name"
msgid "%1$s widget named \"%2$s\" from \"%3$s\" deactivated"
msgstr ""

#: connectors/class-connector-widgets.php:202
#, php-format
msgctxt "1: Name, 3: Sidebar Name"
msgid "%1$s widget from \"%3$s\" deactivated"
msgstr ""

#: connectors/class-connector-widgets.php:205
#, php-format
msgctxt "2: Title, 3: Sidebar Name"
msgid "Unknown widget type named \"%2$s\" from \"%3$s\" deactivated"
msgstr ""

#: connectors/class-connector-widgets.php:208
#, php-format
msgctxt "4: Widget ID, 3: Sidebar Name"
msgid "%4$s widget from \"%3$s\" deactivated"
msgstr ""

#: connectors/class-connector-widgets.php:248
#, php-format
msgctxt "1: Name, 2: Title"
msgid "%1$s widget named \"%2$s\" reactivated"
msgstr ""

#: connectors/class-connector-widgets.php:251
#, php-format
msgctxt "1: Name"
msgid "%1$s widget reactivated"
msgstr ""

#: connectors/class-connector-widgets.php:254
#, php-format
msgctxt "2: Title"
msgid "Unknown widget type named \"%2$s\" reactivated"
msgstr ""

#: connectors/class-connector-widgets.php:257
#, php-format
msgctxt "3: Widget ID"
msgid "%3$s widget reactivated"
msgstr ""

#: connectors/class-connector-widgets.php:304
#, php-format
msgctxt "1: Name, 2: Title, 3: Sidebar Name"
msgid "%1$s widget named \"%2$s\" removed from \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:307
#, php-format
msgctxt "1: Name, 3: Sidebar Name"
msgid "%1$s widget removed from \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:310
#, php-format
msgctxt "2: Title, 3: Sidebar Name"
msgid "Unknown widget type named \"%2$s\" removed from \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:313
#, php-format
msgctxt "4: Widget ID, 3: Sidebar Name"
msgid "%4$s widget removed from \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:357
#, php-format
msgctxt "1: Name, 2: Title, 3: Sidebar Name"
msgid "%1$s widget named \"%2$s\" added to \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:360
#, php-format
msgctxt "1: Name, 3: Sidebar Name"
msgid "%1$s widget added to \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:363
#, php-format
msgctxt "2: Title, 3: Sidebar Name"
msgid "Unknown widget type named \"%2$s\" added to \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:366
#, php-format
msgctxt "4: Widget ID, 3: Sidebar Name"
msgid "%4$s widget added to \"%3$s\""
msgstr ""

#: connectors/class-connector-widgets.php:408
#, php-format
msgctxt "Sidebar name"
msgid "Widgets reordered in \"%s\""
msgstr ""

#: connectors/class-connector-widgets.php:463
#, php-format
msgctxt "1: Name, 2: Title, 4: Old Sidebar Name, 5: New Sidebar Name"
msgid "%1$s widget named \"%2$s\" moved from \"%4$s\" to \"%5$s\""
msgstr ""

#: connectors/class-connector-widgets.php:466
#, php-format
msgctxt "1: Name, 4: Old Sidebar Name, 5: New Sidebar Name"
msgid "%1$s widget moved from \"%4$s\" to \"%5$s\""
msgstr ""

#: connectors/class-connector-widgets.php:469
#, php-format
msgctxt "2: Title, 4: Old Sidebar Name, 5: New Sidebar Name"
msgid "Unknown widget type named \"%2$s\" moved from \"%4$s\" to \"%5$s\""
msgstr ""

#: connectors/class-connector-widgets.php:472
#, php-format
msgctxt "3: Widget ID, 4: Old Sidebar Name, 5: New Sidebar Name"
msgid "%3$s widget moved from \"%4$s\" to \"%5$s\""
msgstr ""

#: connectors/class-connector-widgets.php:585
#, php-format
msgctxt "1: Name, 2: Title, 3: Sidebar Name"
msgid "%1$s widget named \"%2$s\" in \"%3$s\" updated"
msgstr ""

#: connectors/class-connector-widgets.php:588
#, php-format
msgctxt "1: Name, 3: Sidebar Name"
msgid "%1$s widget in \"%3$s\" updated"
msgstr ""

#: connectors/class-connector-widgets.php:591
#, php-format
msgctxt "2: Title, 3: Sidebar Name"
msgid "Unknown widget type named \"%2$s\" in \"%3$s\" updated"
msgstr ""

#: connectors/class-connector-widgets.php:594
#, php-format
msgctxt "4: Widget ID, 3: Sidebar Name"
msgid "%4$s widget in \"%3$s\" updated"
msgstr ""

#: connectors/class-connector-widgets.php:625
#, php-format
msgctxt "1: Name, 2: Title"
msgid "%1$s widget named \"%2$s\" created"
msgstr ""

#: connectors/class-connector-widgets.php:628
#, php-format
msgctxt "1: Name"
msgid "%1$s widget created"
msgstr ""

#: connectors/class-connector-widgets.php:631
#, php-format
msgctxt "2: Title"
msgid "Unknown widget type named \"%2$s\" created"
msgstr ""

#: connectors/class-connector-widgets.php:634
#, php-format
msgctxt "3: Widget ID"
msgid "%3$s widget created"
msgstr ""

#: connectors/class-connector-widgets.php:655
#, php-format
msgctxt "1: Name, 2: Title"
msgid "%1$s widget named \"%2$s\" deleted"
msgstr ""

#: connectors/class-connector-widgets.php:658
#, php-format
msgctxt "1: Name"
msgid "%1$s widget deleted"
msgstr ""

#: connectors/class-connector-widgets.php:661
#, php-format
msgctxt "2: Title"
msgid "Unknown widget type named \"%2$s\" deleted"
msgstr ""

#: connectors/class-connector-widgets.php:664
#, php-format
msgctxt "3: Widget ID"
msgid "%3$s widget deleted"
msgstr ""

#: connectors/class-connector-woocommerce.php:87
msgctxt "woocommerce"
msgid "WooCommerce"
msgstr ""

#: connectors/class-connector-woocommerce.php:97
msgctxt "woocommerce"
msgid "Updated"
msgstr ""

#: connectors/class-connector-woocommerce.php:98
msgctxt "woocommerce"
msgid "Created"
msgstr ""

#: connectors/class-connector-woocommerce.php:99
msgctxt "woocommerce"
msgid "Trashed"
msgstr ""

#: connectors/class-connector-woocommerce.php:100
msgctxt "woocommerce"
msgid "Deleted"
msgstr ""

#: connectors/class-connector-woocommerce.php:121
msgctxt "woocommerce"
msgid "Attributes"
msgstr ""

#: connectors/class-connector-woocommerce.php:141
msgid "Frontend Styles"
msgstr ""

#: connectors/class-connector-woocommerce.php:145
#: connectors/class-connector-woocommerce.php:152
#: connectors/class-connector-woocommerce.php:159
#: connectors/class-connector-woocommerce.php:166
#: connectors/class-connector-woocommerce.php:173
#: connectors/class-connector-woocommerce.php:717
msgid "setting"
msgstr ""

#: connectors/class-connector-woocommerce.php:148
msgid "Gateway Display Default"
msgstr ""

#: connectors/class-connector-woocommerce.php:155
msgid "Gateway Display Order"
msgstr ""

#: connectors/class-connector-woocommerce.php:162
msgid "Shipping Methods Default"
msgstr ""

#: connectors/class-connector-woocommerce.php:169
msgid "Shipping Methods Order"
msgstr ""

#: connectors/class-connector-woocommerce.php:176
msgid "Shipping Debug Mode"
msgstr ""

#: connectors/class-connector-woocommerce.php:180
#: connectors/class-connector-woocommerce.php:187
#: connectors/class-connector-woocommerce.php:194
msgid "tool"
msgstr ""

#: connectors/class-connector-woocommerce.php:183
msgid "Template Debug Mode"
msgstr ""

#: connectors/class-connector-woocommerce.php:190
msgid "Remove post types on uninstall"
msgstr ""

#: connectors/class-connector-woocommerce.php:231
#, php-format
msgid "Edit WooCommerce %s"
msgstr ""

#: connectors/class-connector-woocommerce.php:309
#, php-format
msgctxt "Order title"
msgid "%s created"
msgstr ""

#: connectors/class-connector-woocommerce.php:316
#, php-format
msgctxt "Order title"
msgid "%s trashed"
msgstr ""

#: connectors/class-connector-woocommerce.php:323
#, php-format
msgctxt "Order title"
msgid "%s restored from the trash"
msgstr ""

#: connectors/class-connector-woocommerce.php:330
#, php-format
msgctxt "Order title"
msgid "%s updated"
msgstr ""

#: connectors/class-connector-woocommerce.php:341
#: connectors/class-connector-woocommerce.php:382
#: connectors/class-connector-woocommerce.php:431
msgid "Order number"
msgstr ""

#: connectors/class-connector-woocommerce.php:342
#: connectors/class-connector-woocommerce.php:383
#: connectors/class-connector-woocommerce.php:432
msgid "order"
msgstr ""

#: connectors/class-connector-woocommerce.php:387
#, php-format
msgctxt "Order title"
msgid "\"%s\" deleted from trash"
msgstr ""

#: connectors/class-connector-woocommerce.php:425
#, php-format
msgctxt "1. Order title, 2. Old status, 3. New status"
msgid "%1$s status changed from %2$s to %3$s"
msgstr ""

#: connectors/class-connector-woocommerce.php:464
#, php-format
msgctxt "Term name"
msgid "\"%s\" product attribute created"
msgstr ""

#: connectors/class-connector-woocommerce.php:486
#, php-format
msgctxt "Term name"
msgid "\"%s\" product attribute updated"
msgstr ""

#: connectors/class-connector-woocommerce.php:508
#, php-format
msgctxt "Term name"
msgid "\"%s\" product attribute deleted"
msgstr ""

#: connectors/class-connector-woocommerce.php:532
#, php-format
msgctxt "Tax rate name"
msgid "\"%4$s\" tax rate created"
msgstr ""

#: connectors/class-connector-woocommerce.php:554
#, php-format
msgctxt "Tax rate name"
msgid "\"%4$s\" tax rate updated"
msgstr ""

#: connectors/class-connector-woocommerce.php:586
#, php-format
msgctxt "Tax rate name"
msgid "\"%s\" tax rate deleted"
msgstr ""

#: connectors/class-connector-woocommerce.php:646
#, php-format
msgid "\"%1$s\" %2$s updated"
msgstr ""

#: connectors/class-connector-woocommerce.php:744
msgid "payment gateway"
msgstr ""

#: connectors/class-connector-woocommerce.php:763
msgid "shipping method"
msgstr ""

#: connectors/class-connector-woocommerce.php:782
msgid "email"
msgstr ""

#: connectors/class-connector-woocommerce.php:790
msgid "Tools"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:60
msgctxt "wordpress-seo"
msgid "WordPress SEO"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:70
msgctxt "wordpress-seo"
msgid "Created"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:71
msgctxt "wordpress-seo"
msgid "Updated"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:72
msgctxt "wordpress-seo"
msgid "Added"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:73
msgctxt "wordpress-seo"
msgid "Deleted"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:74
msgctxt "wordpress-seo"
msgid "Exported"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:75
msgctxt "wordpress-seo"
msgid "Imported"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:86
msgctxt "wordpress-seo"
msgid "Dashboard"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:87
msgctxt "wordpress-seo"
msgid "Titles &amp; Metas"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:88
msgctxt "wordpress-seo"
msgid "Social"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:89
msgctxt "wordpress-seo"
msgid "XML Sitemaps"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:90
msgctxt "wordpress-seo"
msgid "Permalinks"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:91
msgctxt "wordpress-seo"
msgid "Internal Links"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:92
msgctxt "wordpress-seo"
msgid "RSS"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:93
msgctxt "wordpress-seo"
msgid "Import & Export"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:94
msgctxt "wordpress-seo"
msgid "Bulk Title Editor"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:95
msgctxt "wordpress-seo"
msgid "Bulk Description Editor"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:96
msgctxt "wordpress-seo"
msgid "Files"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:97
msgctxt "wordpress-seo"
msgid "Content"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:206
msgid "HeadSpace2"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:207
msgid "All-in-One SEO"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:208
msgid "OLD All-in-One SEO"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:209
msgid "WooThemes SEO framework"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:210
msgid "Robots Meta (by Yoast)"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:211
msgid "RSS Footer (by Yoast)"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:212
msgid "Yoast Breadcrumbs"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:221
#, php-format
msgid "Imported settings from %1$s%2$s"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:223
msgid ", and deleted old data"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:243
#, php-format
msgid "Exported settings%s"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:244
msgid ", including taxonomy meta"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:256
#, php-format
msgid "Tried importing settings from \"%s\""
msgstr ""

#: connectors/class-connector-wordpress-seo.php:271
msgid "Tried creating robots.txt file"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:273
msgid "Tried updating robots.txt file"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:275
msgid "Tried updating htaccess file"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:329
#, php-format
msgid "Updated \"%1$s\" of \"%2$s\" %3$s"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:374
#, php-format
msgid "%s settings updated"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:390
msgctxt "wordpress-seo"
msgid "Allow tracking of this WordPress install's anonymous data."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:391
msgctxt "wordpress-seo"
msgid "Disable the Advanced part of the WordPress SEO meta box"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:392
msgctxt "wordpress-seo"
msgid "Alexa Verification ID"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:393
msgctxt "wordpress-seo"
msgid "Bing Webmaster Tools"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:394
msgctxt "wordpress-seo"
msgid "Google Webmaster Tools"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:395
msgctxt "wordpress-seo"
msgid "Pinterest"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:396
msgctxt "wordpress-seo"
msgid "Yandex Webmaster Tools"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:399
msgctxt "wordpress-seo"
msgid "Enable Breadcrumbs"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:400
msgctxt "wordpress-seo"
msgid "Separator between breadcrumbs"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:401
msgctxt "wordpress-seo"
msgid "Anchor text for the Homepage"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:402
msgctxt "wordpress-seo"
msgid "Prefix for the breadcrumb path"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:403
msgctxt "wordpress-seo"
msgid "Prefix for Archive breadcrumbs"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:404
msgctxt "wordpress-seo"
msgid "Prefix for Search Page breadcrumbs"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:405
msgctxt "wordpress-seo"
msgid "Breadcrumb for 404 Page"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:406
msgctxt "wordpress-seo"
msgid "Remove Blog page from Breadcrumbs"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:407
msgctxt "wordpress-seo"
msgid "Bold the last page in the breadcrumb"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:410
msgctxt "wordpress-seo"
msgid "Force rewrite titles"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:411
msgctxt "wordpress-seo"
msgid "Noindex subpages of archives"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:412
msgctxt "wordpress-seo"
msgid "Use <code>meta</code> keywords tag?"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:413
msgctxt "wordpress-seo"
msgid "Add <code>noodp</code> meta robots tag sitewide"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:414
msgctxt "wordpress-seo"
msgid "Add <code>noydir</code> meta robots tag sitewide"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:415
msgctxt "wordpress-seo"
msgid "Hide RSD Links"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:416
msgctxt "wordpress-seo"
msgid "Hide WLW Manifest Links"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:417
msgctxt "wordpress-seo"
msgid "Hide Shortlink for posts"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:418
msgctxt "wordpress-seo"
msgid "Hide RSS Links"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:419
msgctxt "wordpress-seo"
msgid "Disable the author archives"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:420
msgctxt "wordpress-seo"
msgid "Disable the date-based archives"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:423
msgctxt "wordpress-seo"
msgid "Who should have access to the WordPress SEO settings"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:424
msgctxt "wordpress-seo"
msgid "New blogs get the SEO settings from this blog"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:425
msgctxt "wordpress-seo"
msgid "Blog ID"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:428
msgctxt "wordpress-seo"
msgid ""
"Strip the category base (usually <code>/category/</code>) from the category "
"URL."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:429
msgctxt "wordpress-seo"
msgid "Enforce a trailing slash on all category and tag URL's"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:430
msgctxt "wordpress-seo"
msgid "Remove stop words from slugs."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:431
msgctxt "wordpress-seo"
msgid "Redirect attachment URL's to parent post URL."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:432
msgctxt "wordpress-seo"
msgid "Remove the <code>?replytocom</code> variables."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:433
msgctxt "wordpress-seo"
msgid ""
"Redirect ugly URL's to clean permalinks. (Not recommended in many cases!)"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:434
msgctxt "wordpress-seo"
msgid "Force Transport"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:435
msgctxt "wordpress-seo"
msgid "Prevent cleaning out Google Site Search URL's."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:436
msgctxt "wordpress-seo"
msgid ""
"Prevent cleaning out Google Analytics Campaign & Google AdWords Parameters."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:437
msgctxt "wordpress-seo"
msgid "Other variables not to clean"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:440
msgctxt "wordpress-seo"
msgid "Add Open Graph meta data"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:441
msgctxt "wordpress-seo"
msgid "Facebook Page URL"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:442
#: connectors/class-connector-wordpress-seo.php:444
msgctxt "wordpress-seo"
msgid "Image URL"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:443
msgctxt "wordpress-seo"
msgid "Description"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:445
msgctxt "wordpress-seo"
msgid "Add Twitter card meta data"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:446
msgctxt "wordpress-seo"
msgid "Site Twitter Username"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:447
msgctxt "wordpress-seo"
msgid "The default card type to use"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:448
msgctxt "wordpress-seo"
msgid "Add Google+ specific post meta data (excluding author metadata)"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:449
msgctxt "wordpress-seo"
msgid "Google Publisher Page"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:452
msgctxt "wordpress-seo"
msgid "Check this box to enable XML sitemap functionality."
msgstr ""

#: connectors/class-connector-wordpress-seo.php:453
msgctxt "wordpress-seo"
msgid "Disable author/user sitemap"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:454
msgctxt "wordpress-seo"
msgid "Ping Yahoo!"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:455
msgctxt "wordpress-seo"
msgid "Ping Ask.com"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:456
msgctxt "wordpress-seo"
msgid "Max entries per sitemap page"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:459
msgctxt "wordpress-seo"
msgid "Content to put before each post in the feed"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:460
msgctxt "wordpress-seo"
msgid "Content to put after each post"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:464
msgctxt "wordpress-seo"
msgid "Title template"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:465
msgctxt "wordpress-seo"
msgid "Meta description template"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:466
msgctxt "wordpress-seo"
msgid "Meta keywords template"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:467
msgctxt "wordpress-seo"
msgid "Meta Robots"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:468
msgctxt "wordpress-seo"
msgid "Authorship"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:469
msgctxt "wordpress-seo"
msgid "Show date in snippet preview?"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:470
msgctxt "wordpress-seo"
msgid "WordPress SEO Meta Box"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:471
msgctxt "wordpress-seo"
msgid "Breadcrumbs Title"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:472
msgctxt "wordpress-seo"
msgid "Post types"
msgstr ""

#: connectors/class-connector-wordpress-seo.php:473
msgctxt "wordpress-seo"
msgid "Taxonomies"
msgstr ""

#: includes/feeds/atom.php:7 includes/feeds/rss-2.0.php:21
msgid "Stream Feed"
msgstr ""

#: tests/tests/test-class-connector.php:191
msgid "Maintenance"
msgstr ""

#: tests/tests/test-class-connector.php:201
msgid "Fault"
msgstr ""

#: tests/tests/test-class-connector.php:212
msgid "AE35 Unit"
msgstr ""
