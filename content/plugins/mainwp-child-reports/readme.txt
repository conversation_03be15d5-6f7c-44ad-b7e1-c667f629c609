=== MainWP Child Reports ===
Contributors: mainwp
Tags: MainWP Child Reports, MainWP, MainWP Child, MainWP Pro Reports Extension, child reports, reports, actions, activity, admin, analytics, dashboard, log, notification, users, Backupwordpress, Updraftplus
Author: mainwp
Author URI: https://mainwp.com
Plugin URI: https://mainwp.com
Requires at least: 6.0
Tested up to: 6.8.2
Requires PHP: 7.4
Stable tag: 2.2.6
License: GPLv3 or later
License URI: https://www.gnu.org/licenses/gpl-3.0.html

The MainWP Child Report plugin tracks changes to Child sites for the Pro Reports Extension.


== Description ==

**Note: This plugin requires PHP 7.4 or higher to be activated and is only useful if you are using [MainWP](https://wordpress.org/plugins/mainwp/) and the [MainWP Pro Reports Extension](https://mainwp.com/extension/pro-reports/).**

Install the [MainWP Child Plugin](https://wordpress.org/plugins/mainwp-child/) plugin first.

The MainWP Child Report plugin communicates changes on your Child sites to the [MainWP Pro Reports Extension](https://mainwp.com/extension/pro-reports/) in order to create the Pro Reports.

Credit to the [Stream Plugin](https://wordpress.org/plugins/stream/) which the MainWP Child Reports plugin is built on.

== Installation ==

1. Upload the MainWP Child Reports folder to the /wp-content/plugins/ directory
2. Activate the MainWP Child Reports plugin through the 'Plugins' menu in WordPress

== Screenshots ==

1. The MainWP Child Reports Screen
2. The MainWP Child Reports Settings Screen

== Changelog ==

= 2.2.6 - 7-8-2025 =

* Fixed: Resolved an issue where certain context exclusion rules would not be saved properly, improving compatibility with third-party plugins. [(#809)](https://github.com/mainwp/mainwp/issues/809)

= 2.2.5 - 4-29-2025 =

* Fixed: Resolved deprecation warning related to the `translate_meta_boxes` function

= 2.2.4 - 4-22-2025 =

* Fixed: Resolved PHP Warning that occurred due to conflict with the WP Pusher plugin.
* Fixed: Eliminated PHP Error caused by compatibility issues with the WP All Import plugin.
* Updated: Implemented `wp_get_wp_version()` method to retrieve WordPress version instead of using the global `$wp_version` variable for improved reliability. (#762)[https://github.com/mainwp/mainwp/issues/762]

= 2.2.3 - 8-22-2024 =

* Added: Value sanitization during the process of saving Network settings to enhance security and data integrity.

= 2.2.2 - 8-15-2024 =

* Fixed: Conflict with the WooCommerce plugin that caused issues with editing tax rates.
* Updated: Plugin description in the `readme.txt` file for better clarity and information.
* Updated: Adjusted the nonce verification in the network options saving process to occur earlier.

= 2.2.1 - 8-6-2024 =
* Fixed: An issue with adding multiple exclusion rules.
* Enhanced: Security by adding nonce verification.

= 2.2 - 10-31-2023 =
* Fixed: An issue with logging Solid Security scans.
* Fixed: An issue where the theme version number was not correctly logged when specific themes were updated.
* Enhanced: Security by adding nonce verification.
* Removed: Unused code for optimization and cleaner codebase.

[See Video Changelog](https://www.youtube.com/watch?v=Kz_P3sNJuaw)

= 2.1.1 - 5-9-2022 =
* Added: Support for logging actions performed via WP CLI
* Added: Support for logging iThemes Secuirty actions

= 2.1 - 12-14-2022 =
* Updated: PHP 8.1 compatibility improvements
* Preventative: Multiple security enhancements

= 2.0.8 - 9-15-2021 =
* Fixed: An issue with logging certain actions triggered by WP Cron
* Fixed: An issue with displaying timestamps on some setups
* Fixed: Problems with the multibyte string functions usage
* Preventative: Multiple security improvements

= 2.0.7 - 2-4-2021 =
* Fixed: An issue with logging deleted plugins
* Updated: exclusion rules for certain custom post types

= 2.0.6 - 10-29-2020 =
* Added: PHP Docs blocks
* Updated: MainWP Child 4.1 compatibility

= 2.0.5 - 8-31-2020 =
* Fixed: jQuery warning
* Fixed: Compatibility issues with MySQL 8
* Fixed: An issue with logging maintenance tasks

= 2.0.4 - 4-30-2020 =
* Fixed: an issue with logging themes updates
* Fixed: an issue with logging created posts
* Added: option to recreate the plugin database tables
* Added: support for logging WPVivid backups

= 2.0.3 - 2-7-2020 =
* Fixed: an issue logging UpdraftPlus scheduled backups
* Fixed: an issue with dismissing missing database tables warning

= 2.0.2 - 1-22-2020 =
* Fixed: an issue with logging some backups
* Fixed: an issue with logging Maintenance data
* Fixed: an issue with logging security scan data
* Fixed: an issue with displaying empty data

= 2.0.1 - 12-13-2019 =
* Fixed: data Child Reports conversion problem

= 2.0 - 12-9-2019 =
* Added: support for the Pro Reports extension
* Updated: plugin functionality for better performance

= 1.9.3 - 2-14-2019 =
* Fixed: an issue with catching Media upload records
* Fixed: "Undefined variable: branding_header" PHP warning

= 1.9.2 - 1-30-2019 =
* Fixed: an issue with cleaning the plugin database tables on some setups
* Updated: MySQL query improvements

= 1.9.1 - 11-13-2018 =
* Fixed: an issue with missing data fields
* Updated: WooCommerce order notes excluded from showing as comments
* Updated: translation files

= 1.9 - 9-4-2018 =
* Fixed: an issue with recording UpdraftPlus backups
* Added: support for recording WPTC backups

= 1.8 - 8-2-2018 =
* Fixed: an issue with logging plugin installations
* Fixed: an issue with displaying double records
* Fixed: multiple PHP Warnings
* Improved: support for UpdraftPlus backups

= 1.7 - 5-12-2017 =
* Fixed: an issue with recording version numbers
* Fixed: conflict with Select2 library

= 1.6 - 4-4-2017 =
* Fixed: Select2 conflict with WooCommerce 3.0
* Fixed: an issue with returning incorrect date range in reports

= 1.5 - 3-15-2017 =
* Fixed: a few typos

= 1.4 - 2-13-2017 =
* Fixed: an issue with creating database table on first installation

= 1.3 - 2-9-2017 =
* Fixed: an issue with recording duplicate values for UpdraftPlus backups
* Fixed: multiple issues with recording backups made by supported plugins
* Fixed: an issue with recording incorrect values for plugins and themes versions
* Added: support for Wordfence tokens
* Added: support for Maintenance tokens
* Added: support for Page Speed tokens
* Added: support for Broken Links tokens
* Updated: system compatibility updates required by upcoming MainWP Pro Reports Extension version

= 1.2 - 11-9-2016 =
* Fixed: Issue with hiding the plugin in Pro Reports
* Fixed: Conflict with the auto backup feature of the UpdraftPlus Backups plugin (#8435)
* Fixed: Issue with double records for the UpdraftPlus backups
* Fixed: Issue with recording UpdraftPlus and BackUpWordPress backups
* Added: Support for the BackupBuddy plugin
* Added: Support for the MainWP Branding (#10609)

= 1.1 - 4-28-2016 =
* Updated: Support for the MainWP Child Plugin version 3.1.3

= 1.0 - 3-9-2016 =
* Fixed: Issue with recreating tables
* Fixed: Issue with recreating manually deleted tables
* Fixed: Issue with updating actions on auto-save Post and Page
* Fixed: Layout and javascript issue when custom branding is applied
* Added: Feature to copy reports from the Stream plugin
* Added: Support for recording BackWPup backups
* Added: Install Plugins, Install Themes, Delete Plugins, Delete Themes action logging
* Updated: New timeago js library version

* First version - 07-24-15
