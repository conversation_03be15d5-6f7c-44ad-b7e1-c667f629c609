/* Stream Records */

.settings_page_mainwp-reports-page .tablenav {
	padding-top: 6px;
}

.settings_page_mainwp-reports-page #record-actions-form {
	margin-top: -32px;
}

.settings_page_mainwp-reports-page #record-actions-form .button {
	margin-left: 6px;
	float: left;
}

.settings_page_mainwp-reports-page .tablenav .actions {
	padding: 0;
	overflow: visible;
}

#record-query-reset {
	position: relative;
	margin-left: 5px;
	line-height: 28px;
	text-decoration: none;
	display: inline-block;
}

#record-query-reset span.dashicons {
	position: absolute;
	top: 0.5em;
	left: 0;
	font-size: 13px;
}

#record-query-reset .record-query-reset-text {
	margin-left: 19px;
}

.settings_page_mainwp-reports-page .chosen-container-single {
	margin-top: 3px;
	margin-right: 6px;
}

.settings_page_mainwp-reports-page .view-switch {
	display: none;
}

.settings_page_mainwp-reports-page #filter-date-range {
	float: left;
	margin-right: 6px;
}

.settings_page_mainwp-reports-page .manage-column {
	width: 12%;
}

.settings_page_mainwp-reports-page .column-date {
	min-width: 10%;
	white-space: nowrap;
}

.settings_page_mainwp-reports-page .column-date .timeago {
	padding-right: 1em;
}

.settings_page_mainwp-reports-page .column-user_id {
	width: 18%;
}

.settings_page_mainwp-reports-page .column-summary {
	width: auto;
}

.settings_page_mainwp-reports-page .column-ip {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.settings_page_mainwp-reports-page .stream-filter-object-id {
	padding-left: 5px;
	visibility: hidden;
}

.settings_page_mainwp-reports-page td.summary:hover .stream-filter-object-id{
	visibility: visible;
}

.settings_page_mainwp-reports-page .tablenav .stream-export-tablenav {
	margin-top: 6px;
	height: 28px;
	float: right;
}

.settings_page_mainwp-reports-page .tablenav .stream-export-tablenav a {
	margin-top: 0;
}

@media only screen and (min-width: 782px) {
	.settings_page_mainwp-reports-page .tablenav .tablenav-pages {
		margin-bottom: 4px;
	}
	.settings_page_mainwp-reports-page .tablenav .stream-export-tablenav {
		margin-bottom: 4px;
		float: left;
	}
	.settings_page_mainwp-reports-page .tablenav .stream-export-tablenav a {
		margin-top: 5px;
		display: inline-block;
	}
}

@media only screen and (max-width: 782px) {
	.settings_page_mainwp-reports-page .tablenav.bottom .displaying-num {
		top: -8px;
	}
}

@media only screen and (max-width: 900px) {
	.settings_page_mainwp-reports-page .fixed .manage-column,
	.settings_page_mainwp-reports-page .fixed tbody tr td {
		display: none !important;
	}
	.settings_page_mainwp-reports-page .fixed .column-date,
	.settings_page_mainwp-reports-page .fixed .column-summary,
	.settings_page_mainwp-reports-page .fixed .column-user_id,
	.settings_page_mainwp-reports-page .fixed tbody tr.no-items td {
		display: table-cell !important;
	}
	.settings_page_mainwp-reports-page .fixed .column-date {
		width: 100px;
	}
	.settings_page_mainwp-reports-page .fixed .column-user_id {
		width: 50%;
	}
	.settings_page_mainwp-reports-page .fixed .column-summary {
		width: 100%;
	}
}

@media only screen and (max-width: 480px) {
	.settings_page_mainwp-reports-page .fixed .column-user_id {
		display: none;
	}
}

.settings_page_mainwp-reports-page .wp-list-table tr td::before {
	content: "" !important;
}

.settings_page_mainwp-reports-page .column-user_id a {
	vertical-align: top;
}

.settings_page_mainwp-reports-page .column-user_id img {
	float: left;
	margin: 1px 10px 8px 0;
	width: 32px;
	height: 32px;
}

.settings_page_mainwp-reports-page .column-user_id .deleted {
	font-style: italic;
	color: #aaa;
}

.settings_page_mainwp-reports-page .filter-date-range {
	margin-top: -1px;
}

.settings_page_mainwp-reports-page .alignleft.actions input[type=text] {
	height: 28px;
	line-height: 19px;
}

.settings_page_mainwp-reports-page .date-interval {
	display: inline;
}

.settings_page_mainwp-reports-page .select2-container {
	margin-right: 6px;
	margin-bottom: 6px;
}

.settings_page_mainwp-reports-page .select2-container.select2-allowclear .select2-choice abbr {
	margin-top: -2px;
}


/* Live Update */

.settings_page_mainwp-reports-page .stream-live-update-checkbox .spinner {
	margin-top: 5px;
}

.settings_page_mainwp-reports-page .new-row,
#dashboard_stream_activity .new-row {
	background-color: #ffffe0 !important;

	-webkit-transition: background 0.5s linear;
	-moz-transition: background 0.5s linear;
	-ms-transition: background 0.5s linear;
	-o-transition: background 0.5s linear;
	transition: background 0.5s linear;
}

.settings_page_mainwp-reports-page .new-row.alternate,
#dashboard_stream_activity .new-row.alternate {
	background-color: #ffffcd !important;
}

.settings_page_mainwp-reports-page .new-row.fadeout,
#dashboard_stream_activity .new-row.fadeout {
	background-color: transparent !important;
}

.settings_page_mainwp-reports-page .new-row.alternate.fadeout,
#dashboard_stream_activity .new-row.alternate.fadeout {
	background-color: #f9f9f9 !important;
}

.settings_page_mainwp-reports-page #the-list .no-items .stream-list-table-no-items {
	text-align: center;
}

.settings_page_mainwp-reports-page #the-list .no-items .stream-list-table-no-items p {
	margin: 2px 0;
}


/* Settings */

.wp_mainwp_stream_settings .select2.select2-container,
.wp_mainwp_stream_network_settings .select2.select2-container,
.wp_mainwp_stream_default_settings .select2.select2-container {
	min-width: 160px;
	max-width: 100%;
}

.wp_mainwp_stream_settings .tablenav,
.wp_mainwp_stream_network_settings .tablenav,
.wp_mainwp_stream_default_settings .tablenav {
	margin-top: 16px;
}

.wp_mainwp_stream_settings .tablenav input,
.wp_mainwp_stream_network_settings .tablenav input,
.wp_mainwp_stream_default_settings .tablenav input {
	margin-right: 1em;
}

.wp-mainwp-stream-select2-icon {
	position: relative;
	top: 3px;
	margin-right: 4px;
	width: 16px;
	height: 16px;
}

.select2-disabled .wp-mainwp-stream-select2-icon {
	filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale"); /* Firefox 3.5+ */
	filter: gray; /* IE6-9 */
	-webkit-filter: grayscale(100%); /* Chrome 19+ & Safari 6+ */
	-moz-filter: grayscale(100%); /* Firefox < 3.5 */
}


/* Exclude List Table */

.mainwp-stream-exclude-list {
	margin-top: 1em;
}

.mainwp-stream-exclude-list th {
	font-weight: normal;
	padding: 8px 10px;
	width: auto;
}

.mainwp-stream-exclude-list tbody tr.no-items {
	background-color: #fff;
}

.mainwp-stream-exclude-list tbody tr.no-items td {
	padding: 8px 10px;
	font-size: 13px;
}

.mainwp-stream-exclude-list tbody th.check-column {
	padding: 16px 0 0 3px;
}

.mainwp-stream-exclude-list thead th.actions-column {
	width: 3em;
}

.mainwp-stream-exclude-list tbody th.actions-column {
	padding: 21px 10px 20px 0;
}

.mainwp-stream-exclude-list tbody th.actions-column a {
	display: none;
	color: #a00;
	font-size: 13px;
}

.mainwp-stream-exclude-list tbody tr:hover th.actions-column a {
	display: block;
}

.mainwp-stream-exclude-list tbody th.actions-column a:hover {
	color: #f00;
}

.mainwp-stream-exclude-list tbody th.actions-column .dashicons {
	margin-top: 4px;
}

.mainwp-stream-exclude-list tbody td .ip_address {
	width: 100%;
}

.mainwp-stream-exclude-list tbody td .ip_address.invalid {
	border: 1px solid rgba(160,0,0,0.75);
}

.mainwp-stream-exclude-list .icon-users {
	top: -3px !important;
	position: relative !important;
}

.wp_mainwp_stream_screen .select2-results__option .parent {
	font-weight: bold;
}

.wp_mainwp_stream_screen .select2-results__option .child {
	padding-left: 8px;
}

@media screen and ( max-width: 900px ) {
	.wp_mainwp_stream_settings .mainwp-stream-exclude-list .actions-column,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list .actions-column,
	.wp_mainwp_stream_settings .mainwp-stream-exclude-list .actions-column {
		display: none;
	}
}

@media screen and ( max-width: 782px ) {
	.settings_page_mainwp-reports-page #record-actions-form {
		margin-top: 0;
		margin-bottom: 35px;
	}

	.wp_mainwp_stream_settings .mainwp-stream-exclude-list td,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list td,
	.wp_mainwp_stream_default_settings .mainwp-stream-exclude-list td {
		padding: 10px 10px 0 0;
	}

	.wp_mainwp_stream_settings .mainwp-stream-exclude-list th,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list th,
	.wp_mainwp_stream_default_settings .mainwp-stream-exclude-list th {
		display: none;
	}

	.wp_mainwp_stream_settings .mainwp-stream-exclude-list .check-column,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list .check-column,
	.wp_mainwp_stream_default_settings .mainwp-stream-exclude-list .check-column {
		display: table-cell;
		padding: 13px 10px 0 3px;
	}

	.wp_mainwp_stream_settings .mainwp-stream-exclude-list thead .actions-column,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list thead .actions-column,
	.wp_mainwp_stream_default_settings .mainwp-stream-exclude-list thead .actions-column {
		display: table-cell;
		width: auto;
	}

	.wp_mainwp_stream_settings .mainwp-stream-exclude-list thead .actions-column .hidden,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list thead .actions-column .hidden,
	.wp_mainwp_stream_default_settings .mainwp-stream-exclude-list thead .actions-column .hidden {
		display: block;
	}

	.wp_mainwp_stream_settings .mainwp-stream-exclude-list tfoot,
	.wp_mainwp_stream_network_settings .mainwp-stream-exclude-list tfoot,
	.wp_mainwp_stream_default_settings .mainwp-stream-exclude-list tfoot {
		display: none;
	}
}

/* Extensions */

.post-type-stream_notification .view-switch {
	display: none;
}
