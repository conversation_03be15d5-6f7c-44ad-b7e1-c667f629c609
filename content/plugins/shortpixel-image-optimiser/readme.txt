=== ShortPixel Image Optimizer - Optimize Images, Convert WebP & AVIF ===
Contributors: ShortPixel
Tags: convert webp, optimize images, image optimization, resize, compress images
Requires at least: 4.8.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 6.3.3
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Optimize images & PDFs smartly. Create and compress next-gen WebP and AVIF formats. Smart crop and resize.

== Description ==

###🚀 The Ultimate Image Optimization Plugin for WordPress###

**⚡ Boost your site’s speed instantly! Optimize images and PDFs with one click, bulk compress to WebP and AVIF, use lazy loading, and resize images.**

Increase your website's SEO ranking, number of visitors, and ultimately your sales by optimising any image or PDF document on your website.

The <a href="https://shortpixel.com" target="_blank">ShortPixel</a> plugin is a lightweight, user-friendly, install-and-forget solution for image optimization.
It is designed to work with any website type, whether it's a small blog or a large WooCommerce-powered online store with tens of thousands of products.
Additionally, it's an excellent choice for agencies, offering unlimited image optimization credits for a flat monthly fee.

= 🎬 Ready for a Quick DEMO of our Top Image Optimization Tool? =
Test our plugin <a href="https://demo.tastewp.com/shortpixel-image-optimiser" target="_blank">here</a>.
Make an instant <a href="https://shortpixel.com/image-compression-test" target="_blank">image compression test</a> of your site or <a href="https://shortpixel.com/online-image-compression" target="_blank">compress some images</a> to test our optimization algorithms.

== 💡 Why is ShortPixel the best choice for image optimization or PDF compression? ==

### 🆕 New! Freshly added image SEO features ###

* AI-powered image SEO – Automatically generate ALT text, captions, and image descriptions for all your images.
* Bulk mode – Mass-generate and update details for all your images.
* Preview mode – Test and fine-tune AI results before applying new ALT texts and descriptions in bulk.
* Multilingual – Support for 100+ languages.
* WooCommerce – Seamless support for WooCommerce product images.
* Unlimited credits – AI-generate image titles, captions, and more with our Unlimited plan.
* Accessibility – Improve website accessibility by generating all the necessary metadata for each image.

### 🆕 New! Freshly added features ###

* Compress WebP images – ShortPixel now also <a href="https://shortpixel.com/blog/introducing-smartcompress/" target="_blank">smartly compresses</a> all your existing WebP images.
* Added support for serving CSS, JS and fonts from our global CDN.
* Save & Restore option for all settings – ideal for agencies and users managing multiple websites.
* Decide whether AI bots can use your images for machine learning (ML) training, or <a href="https://shortpixel.com/blog/prevent-ai-data-mining-on-images/" target="_blank">block them entirely</a>.

### 🌍 Faster Websites for Global Audiences ###

The ShortPixel plugin now includes a built-in global Content Delivery Network (CDN). This powerful feature ensures that ShortPixel-optimized WebP and AVIF images, as well as your website’s CSS and JavaScript files, are delivered quickly and efficiently to any location worldwide, minimizing delays and improving load times.

By leveraging this built-in solution, you enhance the user experience, reduce server strain, boost SEO performance, and simplify website management — all with minimal effort.

### 📸 Resize and Compress Images Without Losing Quality ###

* Popular plugin with over 300,000 active installations – according to WordPress
* Compress JPG (and its variations: JPEG, JPEG 2000, JPEG XR), PNG, GIF (still or animated) images, and also PDF documents.
* Option to automatically convert PNG to JPG if that results in smaller images (ideal for large PNG pictures).
* CMYK to RGB conversion.
* Progressive JPEG is used whenever it leads to a smaller image.

### 🌟 Lossy and Lossless image compression ###

Both **Lossy and Lossless image compression** are available for the most common image types (JPG, PNG, GIF, WebP, and AVIF) plus PDF files.
We also offer **Glossy** JPEG compression which is a very high-quality lossy optimization algorithm. Especially designed for photographers or for high-quality product pictures.
Optimized images lead to a better user experience, improved PageSpeed Insights or GTmetrix results, higher Google PageRank, and more visitors.

### 🔄 Convert WebP and AVIF Formats ###

* Option to automatically convert JPEG, PNG, or GIF to WebP and AVIF for better performance and improved Google ranking.
* Automatically optimize your existing WebP files with ShortPixel's SmartCompress algorithm.
* Animated GIFs can be automatically converted to much smaller animated WebP or AVIF files.
* Option to include next-gen images (WebP and AVIF) in front-end pages with a single click using the <picture> tag.

### 📦 Bulk Image Optimization and Background Image Processing ###

* New! With 'Background mode,' images can now be optimized without needing to keep a browser tab open.
* Easily add recurring cron jobs for background optimization, ideal for sites where users upload images via the front end.
* Bulk-optimize all images in the Media Library or any gallery with a single click.
* Full WP-CLI support for background processing, especially useful for large Media Libraries.

### 🔥 Performance and Automatic Image Optimization ###

ShortPixel uses minimal resources and works well with any shared, cloud, VPS, or dedicated web hosting. It can optimize any image on your website, including those not listed in the Media Library, such as images in galleries or those added directly via FTP.
All optimization is performed using ShortPixel's Image Optimization Cloud, so your hosting resources remain unaffected.

* New! Automatic scan of Custom media folders for new images using cron jobs.
* Skip already optimized images to avoid redundant processing.

### 🎨 Compatibility with Popular Themes, Page Builders, and Media Library Plugins ###

* Works great for eCommerce websites using WooCommerce and other plugins.
* Compatible with NextGEN Gallery, Modula, Foo Gallery, and other galleries or sliders.
* Fully compatible with WP Retina 2x, including automatic compression of retina images.
* Works seamlessly with WordPress multisite installs (sub-folders or sub-domains) using a single API key.
* Compatible with WPML and WPML Media plugins.
* Fully compatible with WP Offload Media plugin.
* Supports both HTTPS and HTTP websites.
* Compatible with virtually all hosting providers.
* Integrates with Gravity Forms' post_image field type to optimize images upon upload.
* Works with watermarking plugins.
* Integrates directly with Cloudflare via a Cloudflare Token, automatically synchronizing updates with Cloudflare cache.
* **New! HEIC file support**
With ShortPixel, you can now add images in Apple's HEIC format directly from your iPhone. They will be automatically converted to JPG and optimized according to your settings. Easy!
 <a href="https://shortpixel.com/knowledge-base/article/heic-apple-images-support-in-shortpixel-image-optimizer/" target="_blank">Read more</a>.

### 🔧 Advanced Image Optimization Features ###

* NEW Smart Cropping: Generate <a href="https://shortpixel.com/knowledge-base/article/what-is-smart-cropping/">subject-centered</a> thumbnails using AI, ideal for eCommerce websites.
* Optimize thumbnails and featured images, with options to exclude individual thumbnails from optimization.
* Advanced exclusion options (exclude images based on filename, path, size, or complex regex).
* Ability to optimize any image, including those in NextGEN Gallery and other image galleries or sliders.
* Option to scale images down, with two automatic resizing options for large images (applicable to featured images).
* Option to deactivate auto-optimization of images on upload.
* Keep or remove EXIF data from images, which is especially useful for photographers or for enhanced privacy.
* Easily test lossy, glossy, or lossless versions of images with a single click in the Media Library.
* 100MB filesize limit

### 🛡️ Backup and Safety ###

* Safe to test and use: original images are backed up locally and can be restored with a single click (either individually or in bulk).
* No credits are used for images optimized by less than 5%
* Save & Restore option for all settings – ideal for agencies and users managing multiple websites.
* Decide whether AI bots can use your images for machine learning (ML) training, or block them entirely

### 📊 Reporting, Analytics, Compliance and Customer Support ###

* 30-day optimization report with detailed image statistics and overall site performance improvements.
* 24/7 stellar support.
* We are fully GDPR compliant.

**🚀 <a href="https://shortpixel.com/pricing" target="_blank">New Plan: ShortPixel Unlimited</a>**
This is the ideal monthly plan for web agencies or website owners with multiple sites and frequent image uploads.
It allows you to optimize an unlimited number of images with ShortPixel Image Optimizer or use <a href=”https://wordpress.org/plugins/shortpixel-adaptive-images/”>ShortPixel Adaptive Images</a> without worrying about CDN traffic limits.
Read more details on our <a href="https://shortpixel.com/knowledge-base/article/how-does-the-unlimited-plan-work/" target="_blank">dedicated page</a>.

* **free optimization credits for non-profits**, <a href="https://shortpixel.com/contact" target="_blank">contact us</a> for details

**💸 How much does it cost?**
ShortPixel comes with 100 free credits per month, and additional unlimited monthly credits can be purchased for $9.99.
One-time credit packages that never expire are available starting at $19.99.
Check out <a href="https://shortpixel.com/pricing" target="_blank">our prices</a>.

> **🌟 Testimonials:**
> ★★★★★ **A Super Plugin works very well 62% reduction overall.** [robertvarns](https://wordpress.org/support/topic/a-super-plugin-works-very-well-62-reduction-overall/)
> ★★★★★ **The secret sauce for a WordPress website.**  [mark1mark](https://wordpress.org/support/topic/the-secret-sauce-for-a-wordpress-website/)
> ★★★★★ **A must have plugin, great support!** [ElColo13](https://wordpress.org/support/topic/a-must-have-plugin-great-support/)
> ★★★★★ **Excellent Plugin! Even Better Customer Service!**  [scaliendo](https://wordpress.org/support/topic/great-plugin-great-support-508/)
> ★★★★★ **Great image compression, solid plugin, equally great support.** [matters1959](https://wordpress.org/support/topic/support-shortpixel-image-optimiser/)
> [more testimonials](https://wordpress.org/support/plugin/shortpixel-image-optimiser/reviews/?filter=5)

[youtube https://www.youtube.com/watch?v=FVPWeNsJWss]

Help us spread the word by recommending ShortPixel to your friends and collect **100 lifetime monthly additional image credits for each referred active user**. Make money by promoting a great plugin with our <a href="https://shortpixel.com/free-sign-up-affiliate" target="_blank">30% commission affiliate program</a>.

**🛠️ Other plugins by ShortPixel**

* [FastPixel Caching](https://wordpress.org/plugins/fastpixel-website-accelerator/) - WP Optimization made easy
* [ShortPixel Adaptive Images](https://wordpress.org/plugins/shortpixel-adaptive-images/) - On-the-fly image optimization & CDN delivery
* [Enable Media Replace](https://wordpress.org/plugins/enable-media-replace/) - Easily replace images or files in Media Library
* [reGenerate Thumbnails Advanced](https://wordpress.org/plugins/regenerate-thumbnails-advanced/) - Easily regenerate thumbnails
* [Resize Image After Upload](https://wordpress.org/plugins/resize-image-after-upload/) - Automatically resize each uploaded image
* [WP SVG Images](https://wordpress.org/plugins/wp-svg-images/) - Secure upload of SVG files to Media Library 
* [ShortPixel Critical CSS](https://wordpress.org/plugins/shortpixel-critical-css/) - Automatically generate above-the-fold CSS for fatster loading times and better SEO scores

**📩 Get in touch!**

* Email <a href="https://shortpixel.com/contact" target="_blank">https://shortpixel.com/contact</a>
* Twitter <a href="https://twitter.com/shortpixel" target="_blank">https://twitter.com/shortpixel</a>
* Facebook <a href="https://www.facebook.com/ShortPixel" target="_blank">https://www.facebook.com/ShortPixel</a>
* LinkedIn <a href="https://www.linkedin.com/company/shortpixel" target="_blank">https://www.linkedin.com/company/shortpixel</a>

== Installation ==

Let's get the ShortPixel plugin running on your WordPress website:


1. Sign up using your email at <a href="https://shortpixel.com/wp-apikey" target="_blank">https://shortpixel.com/wp-apikey</a>.
2. You will receive your personal API key in a confirmation email, to the address you provided.
3. Upload the ShortPixel plugin to the /wp-content/plugins/ directory
4. Use your unique API key to activate the ShortPixel plugin in the 'Plugins' menu in WordPress.
5. Uploaded images can be automatically optimized in the Media Library.
6. Done!


== Frequently Asked Questions ==

= How does ShortPixel compare to other image optimisation plugins (e.g Smush, Imagify, TinyPNG, Kraken, EWWW, Optimole)?  =
	ShortPixel consistently has better compression rates along with more features, backup support and has very affordable one-time or monthly plans.
	Here are a couple of independent reviews:
	AuthorityHacker - "ShortPixel is our tool of choice, simply because it’s pretty much automated – we just let it do its thing." (<a href="https://www.authorityhacker.com/best-wordpress-image-optimizer/" target="_blank">read full review</a>)
	WP Modula - "One of the reasons I’m personally a huge fan of ShortPixel is the built-in support for next-gen image formats like WebP." (<a href="https://wp-modula.com/the-best-wordpress-image-optimization-plugins/" target="_blank">read full review</a>)

= Can I use the same API Key on multiple web sites? =
    Yes, you can.
    As long as you have available credits, you can use a single API Key on as many websites as you wish!

= What plan is better for me? What do you recommend? =
	Everyone has different needs, but generally, we recommend getting a One-Time plan to optimize your whole Media Library and then get a Monthly plan to optimize your future uploads. To learn more, have a look at <a href="https://shortpixel.com/knowledge-base/article/monthly-plans-vs-one-time-plans/" target="_blank">this comparison</a>

= I don't know how many thumbnails do I have, what plan should I take?
	To know how many thumbnails you have, just install the plugin and go to Media > Bulk ShortPixel. The plugin will tell you how many thumbnails you've got. Based on this, have a look at <a href="https://shortpixel.com/pricing" target="_blank">our plans</a>

= Can I upgrade/downgrade easily my plan? =
	Of course. You can upgrade or downgrade your plans in a couple of clicks from your account on shortpixel.com.

= Can I test/use the plugin for free? =
    Yes, you can.
    We offer 100 free image optimization credits each month. Exceeding the monthly free quota will pause the optimization process till the quota is reset or extended by buying one of our plans.

= Can I optimize images that aren't in Media Library? =
    Absolutely.
    You can actually optimize any image you have on your site regardless of its place. You just need to add - in the Advanced section of the ShortPixel Settings - the folders where the images you want to optimize are located and ShortPixel will work its magic and do the rest.

= Can I optimize images that are both past and new? =
    Sure!
    You can optimize all your past/current images and photos using our "Bulk ShortPixel" page in your Media with a single click.

= A credit = an optimized image? =
    Yes, that is correct.
    But please note that usually, an image in Media Library has 5 or more associated thumbs. Each optimized thumbnail requires a credit. In the rare cases when ShortPixel does not optimise the image (lossy) with at least 5%, the credit will not be consumed, though.

= Can I restore my images? What happens with the originals? =
    If you choose the "Image backup" option in Settings/ShortPixel then the original version of any optimized image or PDF will be saved in the backup folder.
    The original image is needed if you want to restore an image or if you want to convert an image from lossy/glossy to lossless or vice-versa.

= What types of formats can be optimized? =
    ShortPixel optimises JPEG (JPG, JPEG, JPEG 2000, JPEG XR), PNG, GIF (animated and still), and PDF type of files.

= Do you have one-time plans? =
    Yes we do.
    The credits that come with our <a href="https://shortpixel.com/plans" >one-time plans</a> never expire. Yummy! :-)

= What happens to my existing images? =
    Your existing images are replaced with the optimized ones.
    If you choose the backup option then the originals will be saved in a separate folder so you can restore them should you ever need/want to do that.

= How does the plugin work? =
    Our lightweight plugin sends the original images to our Image Optimization Cloud where they are compressed. ShortPixel then downloads the optimized images and the unoptimized originals are replaced with the optimised versions.

= Do you optimize images in the cloud? =
    Yes, all the images processed by ShortPixel are optimized in the Cloud. This takes the load off of your server and allows us to produce the best results.

= What payment methods are accepted? =
    We accept payments via card (Mastercard, Visa, Maestro, American Express, Discover, Diners Club, JCB, UnionPay), PayPal, and Apple Pay.

= How do I activate the API key on a multisite? =
    You have to activate the plugin in the network admin and then activate it manually on each individual site in the multisite. Once you have done that, the Settings menu appears and you can add the API key for each individual site.
    As an alternative, you can edit wp-config.php and add this line:

`define('SHORTPIXEL_API_KEY', 'APIKEY');`
where `APIKEY` is the API Key received upon sign up.
    If configured that way, the API key will be used for all the sites of the multisite but will only be visible on the main site’s Settings page, being hidden for the rest of the sites.

= I am not the only one working on the WordPress Dashboard. How can I hide my API key? =
    There is a simple way to hide the API key, all you need to do is to add these two lines in your wp-config.php:

`define('SHORTPIXEL_API_KEY', '<<your api key here>>');`
`define('SHORTPIXEL_HIDE_API_KEY', true);`

= How much is a credit? =
    A credit is used each time ShortPixel optimizes an image or thumbnail by at least 5%. If we're not able to optimize an image or thumbnail by at least 5% then no credit will be used :-)
    Please also note that usually images in your Media Library have 3-5 thumbs associated and a credit will be used for each featured image or associated thumbnail that is optimized.

= Why shall I use a WordPress plugin and not an offline tool? =
    Because ShortPixel algorithms were perfected while optimizing over 3.5 billion real-life images.
    ShortPixel not only offers the best compression for JPEG, PNG, GIF, and PDF files but it also saves you a lot of time. You just install it on your site and then ShortPixel will take care that all the images on your site are immediately optimized after upload.

= Does optimizing images affect my ALT tags? =
    No, ShortPixel only optimizes images, it won't touch anything else like your HTML/CSS.

= If I stop using ShortPixel will my images remain optimized? =
    Absolutely!
    Once optimized, the images will remain optimized unless you explicitly choose to restore them. But why would you do that? :-)

= Do I have to pay monthly or one time? =
    We have both options available.
    One-time credits never expire and are a bit more expensive. Check out our prices <a href="https://shortpixel.com/pricing" >here</a>

= When can I cancel a monthly plan? =
    Whenever you want.
    The credits you still have available for the current billing period will still be available until the end of the billing period. At the end of it, you won't be billed again and the plan will be reset to the free plan.

= When do credits expire? =
    Monthly credits expire after 30 days while one-time credits never expire.

= Do you have an API? =
    Yes, we have several APIs and tools.
    You can learn more about it here:
    <a href="https://shortpixel.com/api-tools">https://shortpixel.com/api-tools</a>

= Can I use the ShortPixel WP plugin on a localhost installation? =
    Unfortunately not :-(
    But you can use either our command line tool or our web tool
    <a href="https://shortpixel.com/web-tool-docs">https://shortpixel.com/web-tool-docs</a>
    <a href="https://shortpixel.com/cli-docs">https://shortpixel.com/cli-docs</a>

= How does resizing work? =
    If you choose the option to resize images on your site, then the featured image can be resized to a predefined size while keeping its aspect and proportions intact. The associated thumbs won't be resized.
    Using this option you can safely upload original images safely without needing to apply any pre-processing to make them smaller.

= Will ShortPixel work if my website is using CloudFare? =
    Absolutely! Sometimes you'll need to make sure you whitelist some IPs, just <a href="https://shortpixel.com/contact">contact us</a> and we'll assist you with that.

= Where do I report security bugs found in this plugin? =
Please report security bugs found in the source code of the ShortPixel Image Optimizer plugin through the [Patchstack Vulnerability Disclosure Program](https://patchstack.com/database/vdp/shortpixel-image-optimiser). The Patchstack team will assist you with verification, CVE assignment, and notify the developers of this plugin.

= I’m stuck. What do I do? =

The ShortPixel team is here to help. <a href="https://shortpixel.com/contact">Contact us</a>!

= How do I compress images without losing quality in WordPress? =
The best way to compress images without losing quality in WordPress is by using the ShortPixel plugin. ShortPixel automatically compresses images with lossless compression, ensuring the visual quality remains intact while significantly reducing file sizes. Once installed, it optimizes your images in just one click, improving your website's loading speed and SEO performance. You can bulk optimize existing images or compress new ones as you upload them, all without sacrificing quality, making it an ideal solution for maintaining high-quality visuals and fast site performance.

= What is image optimization? =

Image optimization is reducing the file size of images without compromising their quality, to improve website performance. This can be done using tools like ShortPixel, which compresses images, converts them to more efficient formats like WebP, and ensures they are properly sized for faster loading times. Optimized images help improve website speed, enhance user experience, and boost SEO rankings by reducing page load time and bandwidth usage.

= How to optimize a WordPress image? =

The best way to optimize a WordPress image is by using the ShortPixel plugin. ShortPixel automatically optimizes images with both lossless and lossy compression options, reducing file size while keeping high visual quality. Once installed, it optimizes your images in one click, improving your site's load speed and SEO. You can bulk optimize existing images or new uploads, ensuring fast site performance without sacrificing image quality, making it the ideal solution for WordPress image optimization.

= How to SEO optimize images? =

To SEO optimize images in wordpress, use the ShortPixel plugin to compress and convert them without losing quality, reducing load times—a key SEO factor. Add descriptive file names and alt text with relevant keywords to help search engines understand your images. Additionally, resize images to appropriate dimensions and consider converting them to WebP for faster performance. ShortPixel makes this entire process easy, helping improve both your SEO rankings and site speed.

= How do you optimize images for performance? =

To optimize images for performance in WordPress, use the ShortPixel plugin to compress images without sacrificing quality, reducing file sizes for faster load times. Convert images to efficient formats like WebP and ensure they are properly resized to fit your site’s design. ShortPixel automates this process in one click, boosting your website's speed, improving user experience, and enhancing SEO—all key elements for optimal performance.

== Actions and Filters for Developers ==

The ShortPixel Image Optimizer plugin calls the following actions and filters:

`do_action( 'shortpixel_image_optimised', $post_id );`
upon successful optimization;

`do_action("shortpixel_before_restore_image", $post_id);`
before restoring an image from backup;

`do_action("shortpixel_after_restore_image", $post_id);`
after succesful restore;

For version 4.22.10 and earlier:

`apply_filters("shortpixel_backup_folder", $backup_folder, $main_file_path, $sizes);`
just before returning the ShortPixel backup folder, usually /wp-content/uploads/ShortpixelBackups. The `$sizes` are the sizes array from metadata;

For version 5.0.0 and later:

`$directory = apply_filters("shortpixel/file/backup_folder", $directory, $file);`
just before returning the ShortPixel backup folder, usually /wp-content/uploads/ShortpixelBackups).

`apply_filters('shortpixel_image_exists', file_exists($path), $path, $post_id);`
post ID is not always set, only if it's an image from Media Library;

`apply_filters('shortpixel_image_urls', $URLs, $post_id);`
filters the URLs that will be sent to optimisation, `$URLs` is a plain array;

<strong>The filter below is deprecated starting with version 5.0.0!</strong>

`apply_filters('shortpixel/db/chunk_size', $chunk);`
the `$chunk` is the value ShortPixel chooses to use as the number of selected records in one query (based on total table size), some hosts work better with a different value;

For version 4.22.10 and earlier:

`apply_filters('shortpixel/backup/paths', $PATHs, $mainPath);`
filters the array of paths of the images sent for backup and can be used to exclude certain paths/images/thumbs from being backed up, based on the image path. `$mainPath` is the path of the main image, while `$PATHs` is an array with all files to be backed up (including thumbnails);

For version 5.0.0 and later:

`apply_filters('shortpixel/image/skip_backup', false, $this->getFullPath(), $this->is_main_file)`
filters the images that are skipped or not from the backup. Return true for the type of images to be skipped in the backup. If you check if `is_main_file` is true and return false (do not skip backup), while while otherwise returning true, the backup will be kept only for the main image. We suggest using it in conjuction with this action that fires right after the restore from backup is done:

`do_action('shortpixel/image/after_restore', $this, $this->id, $cleanRestore);`
This action can be used to cleanup the meta data from the database, regenerate thumbnails after restoring the main file, writing the updated meta data, etc.

`apply_filters('shortpixel/settings/image_sizes', $sizes);`
filters the array (`$sizes`) of image sizes that can be excluded from processing (displayed in the plugin Advanced settings);

`apply_filters('shortpixel/image/imageparamlist', $result, $this->id, $this);`
filters the list of parameters sent to the API so that resizing can be performed more granularly;

`apply_filters('shortpixel/api/request', $requestParameters, $item_id);`
filters the parameters sent to the optimization API (through `$requestParameters`), described in detail here: <a href="https://shortpixel.com/api-docs" target="_blank">ShortPixel Reducer API</a>; `$item_id` contains the ID of the Media Library item, or the ID of the Custom Media item (when used). In short, this filter can be used to alter any parameters sent to the API, depending on the needs. For example, you can set different resize parameters for different post types, different compression levels, remove EXIF or not, covert WebP/AVIF, and basically any other parameter that is sent to the API for a specific image (together with all its thumbnails).

This filter enables the background ShortPixel processing in additional pages (see <a href="https://shortpixel.com/knowledge-base/article/on-what-pages-does-spio-optimize-images/" target="_blank">here</a> the original list). Here's an example of this filter that enables the processing on the Comments screen (to be placed in your functions.php file):

`
add_filter('shortpixel/init/optimize_on_screens', function ($screens) {
	$screens[] = 'edit-comments';
	return $screens;
});
`
The `edit-comments` is the ID of the screen where you want to enable the processing.

If you want to add multiple pages, here's what the snippet looks like:

`
add_filter('shortpixel/init/optimize_on_screens', function ($screens) {
	$screens = array('edit-comments', 'plugins', 'another-custom-post-type-page');
	return $screens;
	});
`

`add_filter('shortpixel/image/filecheck', function () { return true; });`
This filter forces a file check for WebP/AVIF in case they were manually removed from disk.

If you want to disable the automatic cache flush that is triggered after image optimization, you can use this filter:

`add_filter( 'shortpixel/external/flush_cache', function() { return false; } );`

In order to define custom thumbnails to be picked up by the optimization you have two options, both comma separated defines:

`define('SHORTPIXEL_CUSTOM_THUMB_SUFFIXES', '_tl,_tr');`
will handle custom thumbnails like image-100x100_tl.jpg;

`define('SHORTPIXEL_CUSTOM_THUMB_INFIXES', '-uae');`
 will handle custom thumbnails like image-uae-100x100.jpg;

`define('SHORTPIXEL_USE_DOUBLE_WEBP_EXTENSION', true);`
`define('SHORTPIXEL_USE_DOUBLE_AVIF_EXTENSION', true);`
will tell the plugin to create double extensions for the WebP/AVIF image counterparts, for example, image.jpg.webp/image.jpg.avif for image.jpg;

Enable the "Trusted mode" in case the file system has limitations and is very slow in responding to direct file operations by adding this constant:

`define('SHORTPIXEL_TRUSTED_MODE', true);`

This will simply skip file check operations and if the Media Library loads very slowly or freezes, you might want to try adding the constant above to your wp-config.php file.

Disable the feedback survey when the plugin is deactivated:

`define('SHORTPIXEL_SKIP_FEEDBACK', true);`

Hide the Cloudflare settings by defining these constants in wp-config.php:

`define('SHORTPIXEL_CFTOKEN', 'the Cloudflare API token that has Purge Cache right');`
`define('SHORTPIXEL_CFZONE', 'The Zone ID from the domain settings in Cloudflare');`

Add HTTP basic authentication credentials by defining these constants in wp-config.php

`define('SHORTPIXEL_HTTP_AUTH_USER', 'user');`
`define('SHORTPIXEL_HTTP_AUTH_PASSWORD', 'pass');`

== Screenshots ==

1. Select bulk optimization options. (Media -> Bulk ShortPixel)

2. Bulk optimization running. (Media -> Bulk ShortPixel)

3. Activate your API key in the plugin Settings. (Settings -> ShortPixel)

4. Plugin dashboard after entering the API key. (Settings -> ShortPixel)

5. Image Optimization settings. (Settings -> ShortPixel -> Image Optimization)

6. Exclusion settings. (Settings -> ShortPixel -> Exclusions)

7. Processing settings. (Settings -> ShortPixel -> Processing)

8. Next generation and delivery settings. (Settings -> ShortPixel -> WebP/AVIF and CDN)

9. Integrations. (Settings -> ShortPixel -> Integrations)

10. Tools. (Settings -> ShortPixel -> Tools)

11. Help Center. (Settings -> ShortPixel -> Help Center)

12. Check image optimization status, and restore or reoptimize the image. (Media -> Library)

13. Check image optimisation details. (Media -> Library -> Edit)

14. Check other optimized images' status - themes or other plugins' images. (Media>Other Media)

== Changelog ==

= 6.3.3 =

🧠 The Smarter ALT Tags Update

Release Date: August 21, 2025

🛠️ Fixes & Improvements

* Reliable ALT Tag Replacement: The replacer module now ensures AI-generated ALT tags are updated correctly in all scenarios.
* Cache Purge for Visibility: Automatically clears the cache after ALT tag replacement, ensuring changes show up even when object caching is enabled.
* AI Settings Limits: Added max values for AI context and output fields to prevent excessive input and ensure consistent results.
* UI & Wording Tweaks: Minor text and layout improvements on the settings and bulk processing pages for a cleaner experience.

Update now for smoother AI integration and more accurate image SEO handling! 🚀

= 6.3.2 =

🧠 The AI SEO Compatibility Update

Release Date: August 20, 2025

🛠️ Fixes & Improvements

* Yoast SEO + Classic Editor Fix: Resolved a JavaScript error that occurred when using Yoast SEO alongside the Classic Editor.
* Improved AI Data Layout: The display of AI-generated image SEO data in the Media Library has been refined for better clarity and usability.
* Accurate Undo Function: Undoing AI-generated data from the Media Library popup now correctly reverts all related fields.

Update now for a smoother, more reliable SEO and editing experience! 🚀

= 6.3.1 =

🔥 The Quick Fix Update

Release Date: August 19, 2025

🛠️ Fixes & Improvements

* Draft Content Preservation: AI Image SEO data generation now respects and preserves content from draft posts.
* Cleaner Media Library Display: ShortPixel info is now shown only in the designated column, so no more unexpected placements.
* Classic Editor Compatibility: Resolved JavaScript errors appearing in the post editor when using the Classic Editor.

A focused hotfix to ensure a smoother experience after the latest major release. Update now! 🚀

= 6.3.0 =

✨ The AI Image SEO Update

Release Date: August 18, 2025

🌟 New Features

* Faster AI Model: We've upgraded to a better, faster AI model for generating image SEO data more accurately and efficiently.
* Expanded SEO Tags: In addition to ALT tags, you can now automatically generate image captions and descriptions to enhance SEO and accessibility.
* Auto-Generate on Upload: Image SEO data can now be generated automatically for newly uploaded images.
* Bulk SEO Generation: Easily generate image SEO data in bulk, perfect for optimizing existing media libraries.
* Language Selection: Choose the language used for image SEO generation directly from the plugin settings.

🛠️ Fixes & Improvements

* Cache-Control Headers: WebP and AVIF images now include cache-control headers when delivered via .htaccess, improving cache behavior.
* Thumbnail Handling: If a main image in a special format (like BMP/TIFF) is excluded from optimization, its JPG thumbnails are no longer excluded incorrectly.
* WooCommerce SVG Fix: Fixed an issue where certain SVG icons for payment methods weren’t correctly replaced with CDN links on the WooCommerce cart.

Update now to take full advantage of smarter image SEO with AI and better compatibility across your site! 🚀

= 6.2.2 =

🧠 The Smart Media Update

Release Date: July 29, 2025

⚙️ Improvements

* Enhanced REST API Support: Two new parameters added to the WordPress REST API for media items — source_url_webp and source_url_avif — making it easier to access next-gen image formats programmatically.
* Media Library Filter: Introduced a new filter to hide the ShortPixel box from specific areas in the Media Library — perfect for a cleaner UI when needed.

🛠️ Fixes

* 404 Page Styling: Fixed an issue where CSS was broken on 404 pages when CDN delivery was enabled.
* Large PNG Restore Fix: Resolved errors that occurred when restoring backups of large PNGs with scaled versions generated by WordPress.
* Improved CDN Replacement: Images using data-srcset are now correctly replaced with CDN links when delivery is enabled.
* SmartCropping Upload Issue: Fixed a bug where SmartCropping didn’t work correctly when automatic optimization on upload was turned off.
* Queue Stability: Added a safeguard for rare cases involving old items in the processing queue that could trigger errors.

Update now for better REST API support, improved CDN handling, and a smoother media optimization experience! 🚀


= 6.2.1 =

🚀 The CDN & AI Control Update

Release Date: May 27, 2025

⚙️ Improvements

* AI Feature Control: Added a filter to easily disable AI-powered features if needed.

CDN Delivery Enhancements:
* Now supports background images inside inline style blocks.
* Delivers inline SVGs through the CDN for consistent asset performance.
* Fallback for Missing WebP/AVIF: If WebP or AVIF images aren't generated locally, they’ll now be created on-the-fly and served directly from the CDN.
* Broken Image Protection: Added checks to ensure the CDN doesn't store or deliver broken images.

* Improved Timestamp Handling: Timestamps are now added whenever images are sent for optimization, helping prevent unwanted caching.
* PNG Conversion via ImageMagick: Added fallback support for PNG conversion using ImageMagick when the GD extension isn’t available.
* Punctuation Tweak for AI Texts: AI-generated alternative texts now end with a trailing dot for readability (can be disabled via filter).

🛠️ Fixes

CDN Delivery:
* Fixed multiple edge cases where image URLs with single/double quotes were not parsed correctly.
* Corrected issues with relative paths not resolving to the proper CDN URL.
* Resolved an issue where CDN links were accidentally duplicated in specific situations.

* Settings Import: Fixed import functionality for older PHP versions.
* WebP/AVIF Count Fix: Image counts in bulk processing now display correctly again.
* S3 Uploads Compatibility: Fixed an error triggered by a missing method when using the S3 Uploads plugin (thanks @matthewgrzegorczyk! 🙌).
* WP-CLI Message Bug: Corrected a misleading WP-CLI message shown when thumbnails were unwritable but main images were optimized.
* Custom Media Folder Fix: Resolved errors in the folders list when performing specific actions.

🌍 Language Updates

* Added 2 new strings and updated 2, to enhance global translation support.

Update now to take full advantage of smarter CDN handling, more control over AI features, and improved compatibility across the board! 🚀


= 6.2.0 =

🚀 The Power Tools Update

Release Date: May 8, 2025

🌟 New Features

* Smart WebP Compression: ShortPixel now compresses your existing WebP images to save even more space without sacrificing quality.
* AI-Powered Image Captioning (BETA): Automatically generate meaningful “*alternative text(ALT)”* for your images using our AI model—great for SEO and accessibility!
* Global CDN for CSS & JS: Your CSS, JS, and even background images can now be served via ShortPixel's global CDN for faster page loads.
* CDN Cache Purge Controls: New buttons to purge cached CSS, JS, or the entire CDN cache with one click—right from your dashboard.
* Save & Restore Settings: Export and import all settings effortlessly — ideal for agencies managing multiple sites.

⚙️ Improvements

* Cross-Tab Sync: Optimizations done in multiple browser tabs now stay in sync using browser broadcasting.
* Automatic Association to ShortPixel Account: Your site domain is now automatically associated to your ShortPixel account when enabling CDN delivery.
* CDN Domain Validation: Prevent misconfigurations with smart validation of your CDN domain format.
* Cleaner Admin Bar: The ShortPixel icon is now hidden when restoring items to keep your admin interface tidy.
* CDN Bypass Option: Append `?PageSpeed=off` to disable CDN replacement for debugging or testing.
* WebP Limit Handling: Files that exceed WebP conversion limits are now properly managed to avoid errors.

🛠️ Fixes

* Lossless Labeling Bug: Fixed an issue where images were incorrectly marked as "Lossless" if the main image wasn't optimized.
* Sticky Notification: Resolved an issue where the API key notification couldn't be dismissed after saving it from settings.
* Folder Creation Fix: The plugin no longer attempts to create month-based folders—it now leaves that job to WordPress.
* File Format Conversions: Improved support for HEIC, TIFF, and BMP file conversion.
* API Key Onboarding Fix: Pressing *Enter* now correctly saves your API key during onboarding.

🌍 Language Updates

* Added 87 new strings, updated 2, and deprecated 28 to enhance global translation support.

Update now for faster speeds, smarter automation, and more control than ever! 🚀

= 6.1.4 =

🔧 The Clarity & Stability Update

Release Date: March 27, 2025

🛠️ Fixes & Improvements

* Quota Limit Message: Clearer messaging is now shown on the bulk processing screen when you're out of optimization quota.
* Admin Notice Styling: Fixed visual issues with some admin notifications that weren’t styled correctly in wp-admin.
* Duplicate Image Requests: Resolved an issue where some background images caused doubled image requests when using the PICTURE tag for next-gen formats.
* Redirect Loop Prevention: Added a fail-safe mechanism to avoid potential redirect loops in wp-admin if `register_shutdown_function` fails.

Update now for a cleaner, more stable, and user-friendly experience! 🚀

= 6.1.3 =

🔧 The Smart CDN Update

Release Date: February 13, 2025

🛠️ Fixes & Improvements

* Smarter CDN Replacements: The plugin now verifies if replaced URLs are empty, preventing issues in text templates used by various plugins.
* JSON Compatibility Fix: Resolved cases where JSON files containing images were broken by the CDN replacer.
* Background Processing Cleanup: When disabling background mode, the cron job is now properly removed to avoid unnecessary processes.

This update ensures a more reliable CDN integration, preventing conflicts with plugins and structured data. Update now for a smoother experience! 🚀

= 6.1.2 =

🛒 The Seamless Shopping Update

Release Date: February 11, 2025

🛠️ Fixes

* WooCommerce CDN Fixes: Resolved issues where images in the WooCommerce cart and checkout pages weren’t correctly replaced with CDN links.
* Duplicate Optimization Info: Fixed cases where ShortPixel optimization details appeared twice when using Gutenberg.
* Background Processing Errors: Corrected cron errors affecting background processing in specific scenarios.
* Relative URL CDN Delivery: Ensured proper replacement of relative URLs when using CDN delivery for consistent performance.
* Picture Tag Adjustments: All attributes are now fully supported when delivering next-gen images via the Picture tag method.

🔧 Compatibility Updates

* Avada Live Builder Integration: Automatically disabled the CDN replacer when using Avada Live Builder to prevent conflicts.

✨ Tweaks & Improvements

* UI Refinements: Enhanced CSS styling, clearer texts, and smoother layouts to improve the onboarding experience for new users.

🌍 Language Updates

* Added 2 new strings, updated 3, with no deprecated strings to improve global translation coverage.

Update now to keep your WooCommerce store and website running seamlessly! 🚀

= 6.1.1 =

🔧 The needed Friday release

Release Date: January 24, 2025

🛠️ Fixes

AVIF Notification Disabled: The AVIF notification got a little crazy and started appearing where it shouldn’t. We’ve completely disabled it to avoid any further confusion. Apologies for the inconvenience!

Thank you for your patience and understanding! Update now for a smoother experience. 🚀

= 6.1.0 =

🚀 The Data-mining Update

Release Date: January 23, 2025

🎉 New Features

* AI Training Control: Added data-mining options in the EXIF management settings. You can now decide whether your images can be used for AI training.
* Improved Switchers Design: Enjoy a cleaner and more user-friendly design for the switchers in settings and bulk processing.
* AVIF Recheck Option: Added a handy recheck option to notifications about AVIF issues for better troubleshooting.

🛠️ Fixes

* Resolved "Could not save backup" errors caused by specific combinations of thumbnail sizes and SmartCropping.
* Fixed missing icons or images in certain notifications.
* Custom Media folders "Last change" timestamps are now updated correctly when changes occur.
* Proper detection of Custom Media images missing WebP or AVIF formats in all scenarios.
* Fixed display issues with Custom Media notices when selecting new folders.
* Removed old Custom Media cron formats when the plugin is deactivated.
* Prevented errors by ensuring thumbnails aren’t added to the optimization queue when the main image isn’t processable.
* Resized images through filters get their metadata updated correctly.

✨ Tweaks & Improvements

* WP-CLI Processes: Updated process stats every 3 minutes for accurate numbers.
* Custom Media Cron: Cron jobs for new file detection won’t run if the Custom Media option is disabled.
* Added a filter for settings to enable programmatic changes when needed.
* Updated all plugin links to avoid unnecessary redirects.
* Added support for CDN on http-only websites.
* Polished CSS, texts, and layouts for a smoother experience.
* Old, unused code has been cleaned up.

🌍 Language Updates

* Added 16 new strings, updated 7, and deprecated 67 to enhance global translation support.

Update now to take full advantage of these enhancements and new features! 🌟

= 6.0.5 =
Release Date: January 16, 2025

🛠️ Fixes

* Resolved an issue where WebP or AVIF formats were not added to bulk processing for Custom Media items.
* Fixed a deprecation warning that appeared when using PHP 8.3.
* Corrected a redirect error after saving the API Key, which previously led to the general WordPress settings.
* Fixed a misleading message displayed when a converted image was restored from backup.
* Added a proper link to Custom Media images when displaying errors after the bulk processing.

🔧 Compatibility Updates

* CDN Replacer Disabled: Now automatically disabled when using Bricks, Breakdance, or Oxygen builders to avoid conflicts.

✨ Tweaks & Improvements

* Updated Texts: Improved clarity in messages when bulk processing is paused and background mode is active.
* API Key Box Hidden: Completely hide the API Key box when the SHORTPIXEL_HIDE_API_KEY constant is used.

🌍 Language Updates

* Added 4 new strings, updated 2 strings, and deprecated 4 strings to improve translation coverage.

Update now to enjoy these enhancements and fixes for a smoother experience! 🚀

= 6.0.4 =
Release date: December 10, 2024
* Fix: Inline images and SVGs are no longer replaced by CDN links;
* Fix: The text domain load has been removed from the plugin code to avoid future errors;
* Fix: When trying to clear the queue, the settings page was not loaded correctly;
* Fix: Forced generation for PNGs could be enabled even if the PNG2JPG option was disabled;
* Tweak: Several minor CSS and wording improvements have been added;
* Tweak: Bulk preparation now runs faster when no images are optimized;
* Language: 0 new strings added, 2 updated, 0 fuzzed and 0 deprecated.

= 6.0.3 =
Release date: December 3, 2024
* Fix: With certain WooCommerce themes and CDN delivery enabled, some images were not loaded on the cart and checkout pages
* Fix: Removed an unused hook that was triggering errors in some cases;
* Fix: A JavaScript error was displayed in the browser console when CDN delivery was hidden in the settings;
* Fix: Divi frontend builder now works when CDN delivery is enabled;
* Language: 2 new strings added, 0 updated, 0 fuzzed and 0 deprecated.

= 6.0.2 =
Release date: November 28, 2024
* Fix: Resizing and SmartCropping can now both be enabled;
* Fix: Beaver Builder now works when CDN delivery is enabled;
* Fix: In some cases the picture tag was generated even if it was disabled;
* Fix: Some notifications still had links pointing to the old settings structure;
* Fix: The custom media cron generated an error under very specific conditions in PHP 8.2.x;
* Fix: More CSS changes to make the settings usable when other plugins insert their own CSS in wrong places;
* Fix: All database options are now removed from the tools section after uninstalling the plugin;
* Fix: Added a possible fix if the execution of the installation scripts hangs after upgrading to version 6;
* Fix: Some JavaScript errors were displayed in the Theme Customizer section;
* Tweak: Added filter to hide the new CDN delivery method;
* Tweak: Removed a database query for custom media that was no longer needed in wp-admin;
* Tweak: The save settings button function has been improved and prevents multiple saves at once;
* Tweak: The default CDN URL has been updated;
* Language: 1 new strings added, 10 updated, 0 fuzzed and 0 deprecated.

= 6.0.1 =
Release date: November 22, 2024
* Fix: Some PHP warnings were displayed for certain images when they were optimized;
* Fix: The plugin settings are loaded correctly even if the Google Reviews & Ratings plugin is active;
* Fix: Help icon pop-ups now look better;
* Language: 0 new strings added, 0 updated, 0 fuzzed and 0 deprecated.

= 6.0.0 =
Release date: November 21, 2024
* New: The plugin now also delivers next generation images such as WebP and AVIF via the ShortPixel CDN;
* New: Complete redesign of the plugin settings and layout;
* New: Overview page with image status in a single view;
* New: Simple and advanced settings modes are now available;
* Tweak: Plugin processing is no longer started for users without appropriate rights (such as Authors);
* Fix: The filter "optimized/not optimized" from the Media Library now works correctly when the main image is excluded;
* Fix: The comparator in the NextGen Gallery now looks better;
* Fix: When using WPML, random optimization errors occurred with some images;
* Language: Many of the plugin strings have been updated or changed and we thank the translation teams for their efforts..

= 5.6.4 =
Release date: October 9, 2024
* Fix: A Broken Access Control vulnerability has been fixed (safely disclosed by the PatchStack team - thanks!);
* Fix: An SQL injection vulnerability has been patched (also safely disclosed by the PatchStack team);
* Fix: Moved loading of the plugin text domain to the init hook to increase compatibility;
* Language: 2 new strings added, 0 updated, 0 fuzzed, and 0 deprecated.

= 5.6.3 =
Release date: July 16, 2024
* Compat: Integration with Polylang Pro is fixed;
* Fix: Some bulk actions in the list view of the Media Library did not work;
* Fix: PHP warning was displayed when an existing Custom Media folder was no longer present on the disk;
* Language: 0 new strings added, 0 updated, 0 fuzzed, and 0 deprecated.

= 5.6.2 =
Release date: May 23, 2024
* Fix: The Cover or Contain setting for resizing was not retained when saving;
* Language: 0 new strings added, 0 updated, 0 fuzzed, and 0 deprecated.

= 5.6.1 =
Release date: May 21, 2024
* Fix: Folders from the uploads folder can now be selected as Custom Media even if the year/month structure is not used;
* Fix: In certain cases PHP errors were generated when trying to optimize or even upload images;
* Fix: Some typos caused warnings in some cases;
* Fix: Fixed a typo regarding the number of seconds in a day (kudos to @JusGu for pointing this out);
* Fix: Images with a size of 0Kb are no longer processed;
* Compat: The integration with Formidable Forms now works directly in the Media Library (no need for Custom Media anymore);
* Language: 5 new strings added, 2 updated, 1 fuzzed, and 2 deprecated.

= 5.6.0 =
Release date: May 2, 2024
* New: Added background mode that uses cron jobs to perform optimizations without having to keep the browser open;
* New: Custom media folders are now automatically checked for new images in the background with cron jobs;
* New: The bulk actions from the list view of the media library have been moved to JS;
* New: BMP and TIFF files are automatically converted to JPG;
* Compat: Added integration for images uploaded via Formidable Forms;
* Compat: Improved performance of PICTURE tag delivery mode when offload is enabled;
* Compat: The images generated by the Uncode theme are now automatically optimized;
* Compat: PNG conversion has been improved to better handle images with alpha channel but without transparent pixels;
* Compat: Added a filter to disable the PHP basedir constraints check;
* Compat: Added filter to allow generation of WebP/AVIF even if they are larger than JPG/PNG;
* Compat: Added new hooks for Regenerate Thumbnails Advanced;
* Compat: Added notification to enable double WebP extension when WebP delivery is enabled in Litespeed Cache;
* Compat: Fixed a conflict with the Getty Images plugin (thanks @pintofbeer);
* Compat: Fixed Photo Engine/WpLr integration when republishing images from Adobe Lightroom;
* Fix: PDF files are now correctly excluded from processing when the option is disabled in the advanced settings;
* Fix: Styling corrected when deactivating the plugin;
* Fix: Added additional checks for silent mode when errors occurred in some cases;
* Fix: Some PHP warnings were displayed under PHP 8.3;
* Fix: The warning about Imagify could not be dismissed;
* Language: 27 new strings added, 6 updated, 0 fuzzed, and 0 deprecated.

= 5.5.5 =
Release date: February 13, 2024
* Compat: Added new hooks to be used by the Regenerate Thumbnails Advanced plugin;
* Compat: Adjusted the settings limitations for the new Unlimited plans;
* Fix: Added an additional check to prevent database table errors when the plugin is removed;
* Language: 0 new strings added, 1 updated, 0 fuzzed and 0 deprecated.

= 5.5.4 =
Release date: January 22, 2024
* Tweak: The messages displayed when executing WP-CLI "auto" commands have been improved;
* Tweak: More options and flexibility for the filter used when creating the `.htaccess` rules (for WebP delivery);
* Tweak: Added a check and mechanism to prevent running out of memory if the bulk process preparation fails due to lack of memory;
* Tweak: Added back the "Optimize" option in the "Bulk actions" section in the list view of the Media Library;
* Compat: Added integration with the "Media File Renamer" plugin;
* Fix: Under PHP 8.1+, a notice was displayed when re-optimizations were performed;
* Fix: A PHP warning was displayed converting optimization metadata via WP-CLI;
* Fix: Multiple exclusion checkboxes in the plugin settings can now be selected with the SHIFT key;
* Fix: Converting optimization metadata without backups could cause the main images to be incorrectly set to unprocessed;
* Language: 4 new strings added, 0 updated, 0 fuzzed, and 0 deprecated.

= 5.5.3 =
Release date: January 3, 2024
* Fix: In some cases, an error was displayed when adding a folder to Custom Media, even if the folder was added correctly;
* Fix: For WebP files uploaded directly to the Media Library, the plugin could remove the wrong files during certain operations;
* Fix: Bulk processing of Custom Media could lead to errors in some cases;
* Fix: A PHP Warning was displayed on the Custom Media page if no items were present;
* Language: 0 new strings added, 0 updated, 0 fuzzed and 0 deprecated.

= 5.5.2 =
Release date: November 24, 2023
* Fix: The excluded thumbnails are no longer checked out when saving the settings;
* Fix: Adding new exclusions is now fixed;
* Fix: Updated the wording of exclusions to make them consistent;
* Fix: An error was displayed in the logs if an image could not be loaded at all;
* Fix: An error is now displayed when a Custom Media folder cannot be added instead of silently crashing the page;
* Fix: If WP_CONTENT_DIR does not contain wp-content, adding a Custom Media folder failed;
* Language: 8 new strings added, 2 updated, 0 fuzzed and 7 deprecated.

= 5.5.1 =
Release date: November 17, 2023
* New: Added a filter to change the parameters sent to the API (e.g. for granular resizing);
* Fix: Some PHP notices were displayed in the logs when no exclusions were set;
* Fix: when using the Media Library Grid view, a request for a non-existent image resulted in an Ajax error;
* Fix: Add checks to prevent errors when WebP files cannot be copied to their destination;
* Tweak: Removed notification about HEIC files;
* Language: 0 new strings added, 0 updated, 0 fuzzed and 2 deprecated.

= 5.5.0 =
Release date: November 8, 2023
* New: The Custom Media folders have been redesigned, more features have been added and everything has been moved to Media -> Custom Media;
* New: The exclusions have been redesigned to provide a simpler and more flexible mechanism;
* New: The ability to override exclusions and manually optimize excluded images has been added;
* New: The ability to mark images as completed (optimized) has been added;
* New: Added migration of optimization data to WP-CLI;
* New: Added constant to disable the feedback survey when the plugin is deactivated;
* Compat: Added proper validation and integration for the Swift AI plugin;
* Compat: Added filter to suppress cache clearing for various cache plugins (useful for large websites);
* Fix: If an image has been excluded, it can now be restored from the backup;
* Fix: Envira and Soliloquy have been added to the list of pages where processing is performed automatically;
* Fix: Websites with many Custom Media folders and/or NextGen galleries should no longer hang;
* Fix: Added some checks and validations to avoid `open_basedir` warnings;
* Fix: Updated wording and banners throughout the plugin;
* Language: 94 new strings added, 3 updated, 5 fuzzed and 33 deprecated.

= EARLIER VERSIONS =
* please refer to the <a href="https://github.com/short-pixel-optimizer/shortpixel-image-optimiser/blob/master/changelog.txt" target="_blank">changelog.txt</a> file inside the plugin archive.

== Upgrade Notice ==

= 5.0.0 =

* BIG UPDATE: This version is a major rewrite of the plugin. We strongly suggest you take a FULL BACKUP (DATABASE and IMAGES) before upgrading because there are multiple changes in the code and the optimization information is stored in a different way starting with this version.

= 4.22.9 =

* This version contains a fix for the WP Offload Media plugin version 2.6.0 and above. Please ensure you're running the latest WP Offload Media plugin and check that everything works correctly after upgrading.

