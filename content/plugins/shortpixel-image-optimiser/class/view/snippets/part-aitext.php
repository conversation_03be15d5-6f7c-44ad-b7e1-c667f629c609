<?php

use ShortPixel\Controller\ApiKeyController;
use ShortPixel\Controller\QuotaController;

$icon_url = plugins_url( '/res/images/icon/', SHORTPIXEL_PLUGIN_FILE ); 
$robo_icon = $icon_url . '/shortpixel.svg'; 
$ai_icon = $icon_url . '/ai.svg'; 

$item_id = $this->data['item_id']; 
$has_data = $this->data['has_data'];
$isSupported = $this->data['isSupported'];
$isDifferent = $this->data['isDifferent'];
$dataItems = implode(',',$this->data['dataItems']);

$quotaControl = QuotaController::getInstance();
$keyControl = ApiKeyController::getInstance();


if (true === apply_filters('shortpixel/settings/no_ai', false)):

	?>
	<p class='hidden' id='shortpixel-noai'></p>
<?php

elseif (false === $isSupported):

	?>
		<p><?php _e('Currently, ShortPixel AI cannot generate SEO data for GIF files.', 'shortpixel-image-optimiser'); ?></p>
	<?php 

elseif (false === $has_data): 

	if (false === $keyControl->keyIsVerified() && false === $has_data):
	
  		_e('Invalid API Key. <a href="options-general.php?page=wp-shortpixel-settings">Check your Settings</a>','shortpixel-image-optimiser');

	elseif (false === $quotaControl->hasQuota() && false === $has_data):
	?>
	<p><?php _e('ShortPixel quota exhausted, the AI SEO data cannot be generated.', 'shortpixel-image-optimiser'); ?></p>

	<?php else: ?>

        <a class='button button-secondary' title="Generate image SEO data with ShortPixel AI (Beta)" href="javascript:window.ShortPixelProcessor.screen.RequestAlt(<?php echo $item_id ?>)">
			<img class="shortpixel-ai-icon ai" src="<?php echo $ai_icon ?>">	
			<span><?php printf(__('AI image SEO by ShortPixel %s', 'shortpixel-image-optimiser'), $dataItems) ?></span>
			<img class="shortpixel-ai-icon shortpixel" src="<?php echo $robo_icon ?>">
		</a>
        <div class="shortpixel-alt-messagebox" id="shortpixel-ai-messagebox-<?php echo $item_id ?>">&nbsp;</div>
	<?php endif; ?>   
<?php else : 
	
	$lb = '%0D%0A';
	$mailtolink = "mailto:<EMAIL>?Subject=Wrong AI SEO data generated by ShortPixel AI&Body=ImageURL:  " . $this->data['image_url'] . "$lb $lb Caption:  " . $this->data['result_alt'];
?>

	<p><img class="shortpixel-ai-caption shortpixel-ai-icon shortpixel" src="<?php echo $robo_icon ?>"> <?php printf(__('The following image SEO data was generated by ShortPixel AI: %s', 'shortpixel-image-optimiser'), '<strong>' . $dataItems . '</strong>') ?></p>
	
	<a class='button button-secondary' title="<?php _e('Restores the previous image SEO data, if available.', 'shortpixel-image-optimiser'); ?>" href="javascript:window.ShortPixelProcessor.screen.UndoAlt(<?php echo $item_id ?>, 'undo')"><?php _e('Undo', 'shortpixel-image-optimiser'); ?></a>

	<?php if (true === $isDifferent): ?>

	<a class='button button-secondary' title="<?php _e('Redo', 'shortpixel-image-optimiser'); ?>" href="javascript:window.ShortPixelProcessor.screen.UndoAlt(<?php echo $item_id ?>, 'redo')"><?php _e('Redo', 'shortpixel-image-optimiser'); ?></a>
	<?php endif; ?>

	<a class='button button-secondary' title="<?php _e('Report to us, via e-mail, an incorrect image SEO data for this image', 'shortpixel-image-optimiser') ?>." href="<?php echo $mailtolink ?>"><?php _e('Report', 'shortpixel-image-optimiser'); ?></a>

	<a class='button button-secondary' title="<?php _e('Send us feedback about this feature, suggest other features, or vote on existing feature requests', 'shortpixel-image-optimiser'); ?> ." target="_blank" href="https://ideas.shortpixel.com/"><?php _e('Feedback', 'shortpixel-image-optimiser'); ?></a>

	<?php if (true === $isDifferent)
	{
		 printf(__('%s The plugin has detected some fields were changed after generating AI Data. You can use the redo button to regenerate AI Data %s ', 'shortpixel-image-optimiser'), '<p>', '</p>');
	} ?>

<?php endif; ?>
