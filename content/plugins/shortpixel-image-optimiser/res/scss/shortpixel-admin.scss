@import 'elements/animation';
@import 'elements/actionbar';
@import 'elements/colors';

@import 'view/edit-media';
//@import 'view/other-media';
@import 'elements/icons';

@import 'view/list-item';
@import 'view/modal';
@import 'view/debug';



// General Styles ( mostly from style.css )

// Used in modals around
.shortpixel-hide {
    display: none !important;
}

// Reserve some space for the processing text on bottom.
.button-primary.optimize
{
	 margin-bottom: 16px;
}

.switch_button, switch
{
	//margin-left: -5px;
	//line-height: 12px;
	//margin: 2px -5px 10px -5px ;
	display: inline-block;

	label{
		padding: 8px 5px; // this padding to make clicking more flexible ( bigger hitbox )

		&:focus {
			outline: none;
		}
	}
	input[type="checkbox"] { display: none;
		&:checked ~ .the_switch { background: $blue_default; } // background of bar when active
		&:checked ~ .the_switch:after {
			//left: 20px;
			transform: translateX(19px);
			background-color: $blue_dark; // color of circle when active
		}
		&:disabled ~ .the_switch{
			background: #d5d5d5;
			pointer-events: none;
		}
		&:disabled ~ .the_switch:after { background: #bcbdbc; }
	}
	.the_switch {
		margin: 8px 15px 8px 0;
		position: relative;
		display: inline-block;
		height: 20px;
		width: 40px;
		background-color: #D7D7D7; // color of bar when not active
		border-radius: 30px;
		cursor: pointer;
		transition: background-color 0.4s;
		//box-shadow : inset 0 1px 1px rgba(0, 0, 0, 0.25) !important;

		&:after {
			position: absolute;
			left: 2px;
			//top: -5px;
			bottom: 2px;
			//display: block;
			width: 16px;
			height: 16px;
			border-radius: 50%;
			background-color: #A9A9A9;
			transition: transform 0.4s;
			//box-shadow: 0px 1px 2px rgba(0,0,0,0.31);
			content: '';

			// bouncy effect
			//-webkit-transition : left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.5s ease, margin 0.5s ease !important;
			//-o-transition      : left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.5s ease, margin 0.5s ease !important;
			//transition         : left 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), padding 0.5s ease, margin 0.5s ease !important;
		}
		&:active:after {} //transform: scale(0.9, 0.85); }
		&:focus {
			outline: none;
		}
	}

}

// The advanced switch
.adv_switch{
  position: relative;
  display: inline-block;
  width: 45px;
  height: 19px;
  bottom: 2px;
  left: 20px;

  // Hide the default checkbox
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  // The slider
  .adv_slider {
    position: absolute;
    cursor: pointer;
    background-color: $color-grey; // Unchecked background color
    border-radius: 34px;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transition: 0.4s;

    // The circle inside the slider
    &::before {
      position: absolute;
      content: "";
      height: 13px;
      width: 13px;
      left: 3px;
      bottom: 3px;
      background-color: #A9A9A9; // Unchecked ball color
      border-radius: 50%;
      transition: 0.4s;
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
    }

    // The label
    &::after {
      position: absolute;
      content: "OFF";
      color: white;
      font-size: 10px;
      top: 9px;
      transform: translateY(-50%);
      right: 5px;
      transition: 0.4s;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
      font-weight: bold;
    }
  }

  // Change background color when checked
  input:checked + .adv_slider {
    background-color: $green_highlight; // Checked background color

    // Move the circle to the right and change its color when checked
    &::before {
      transform: translateX(26px);
      background-color: #fff; // Checked ball color
    }

    // Change label when checked
    &::after {
      content: "ON";
      left: 8px;
      right: auto;
    }
  }
} // adv_switch 

.shortpixel-ai-interface {
  line-height: 1; 
  margin: 8px 0; 

  .shortpixel-ai-icon 
  {
     vertical-align: middle;
     height: 20px;
     width: 20px;
     margin-top: -1px;

  }
  .shortpixel-ai-caption
  {
    margin: 0 4px;
  }
  span {
    vertical-align: middle; 
    margin: 0 4px; 
  }

  .shortpixel-alt-messagebox
  {
     margin: 4px 0;
  }

  &.attachment-details-two-column-alt-text, &.attachment-details-alt-text
  {
    float: right;
    width: 65%;
    & img.ai {
      margin-top: 5px;
      float: left;
    }
    & img.shortpixel {
      margin-right: 10px;
    }
  }

  @at-root .media-sidebar & {
    width: 100%;
    text-align: right;
    & img.shortpixel.shortpixel-ai-caption {
      margin-right: 0px;
    }
  }

}
