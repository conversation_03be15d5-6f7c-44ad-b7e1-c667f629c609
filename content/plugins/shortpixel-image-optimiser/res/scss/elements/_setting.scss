// A settings setting (tm)

setting {

  padding: 12px;
  background-color: $blue_background;
  border-radius: 0.7rem;
  margin: 10px 0;
  display: block;
  width: auto;
  height: auto;
  position: relative;
  transition: all 0.5s ease-in;

  //display: flex;
  // align-items: center;
  //flex-wrap: wrap;
  //flex-direction: column;

  // when showing on a toggle, force flex.
  /*  &.toggleTarget.is-visible
  {
     //display: flex !important;
  } */

  &#compression-type {
    margin-bottom: 30px;
  }

  // main title of setting
  name {
    //    width: 220px;
    //    min-width: 220px;
    font-size: 14px;
    font-weight: 700;
    display: block;
    padding: 6px 10px 0 0;
    box-sizing: border-box;
    color: $blue_dark;

  }

  content {
    padding: 5px 10px;
    flex-basis: calc(100% - 250px);
    font-size: 14px;
    display: inline-block;
    &.nextline {
       display: block;
    }

    &.shortpixel-compression {
      max-width: calc(100% - 260px);
    }

    // Label for text input next to the input box.
    inputlabel {
      min-width: 100px;
      font-weight: 700;
      display: inline-block;
    }

    input[type="text"], input[type="number"] {
       width: 290px; 
       height: 36px;
       border: 1px solid #1ABDCA;
       background: #fff; 
       &[type="number"]
       {
         width: 80px;
       }
    }

  }

  // additional information
  info {
    display: block;
    color: #818181;
    font-size: 13px;
    margin: 6px 0;
    &.fieldmessage {
       font-weight: 700;
       color: #ff0000;
    }
  }

  warning,
  error {
    border-left: 4px solid #ffb900;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
    padding: 8px;
    //margin-left: 230px;
    margin: 4px 0 12px 0px;
    flex-basis: 100%;
    display: none;
    opacity: 0;
    transition: opacity 300ms;
    background: #fff;
    // Looks better with full width
    //max-width: 700px;

    h4 {
      margin: 0;
      font-size: 16px;
    }

    p {
      color: #333;
      font-size: 13px;
    }

    &.is-visible {
      display: block;
      opacity: 1;
    }
  }

  error {
    border-left-color: #ff0000;
  }

  switch {
    label {
      font-weight: 700;
    }
  }


  &.disabled {
    filter: blur(1px);
    opacity: 0.4;
    transition: all 0.5s ease-out;
  }

  &.textarea 
  {
     content {
       display: block;
     }
  }

}

// setting