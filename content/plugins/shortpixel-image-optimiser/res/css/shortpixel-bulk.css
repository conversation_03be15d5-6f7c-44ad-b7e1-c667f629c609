@font-face {
  src: url("../fonts/Roboto-Regular.ttf") format("truetype");
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
}
@font-face {
  src: url("../fonts/Roboto-Bold.ttf") format("truetype");
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
}
.wrap.is-shortpixel-bulk-page {
  /**** DASHBOARD ***/
  /** GENERAL STYLES **/
  /*  h1 {
      font-size: 23px;
      font-weight: 400;
      line-height: 30px;
      margin: 30px 0;
    } */
  /***************** SELECTION *************/
  /**************** SUMMARY ******/
}
.wrap.is-shortpixel-bulk-page header {
  background: #fff;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px auto;
  margin-left: -22px;
  margin-top: -10px;
  margin-right: -20px;
  margin-bottom: 10px;
  padding: 8px 16px;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
}
.wrap.is-shortpixel-bulk-page header h1 {
  line-height: 50px;
}
.wrap.is-shortpixel-bulk-page header img {
  vertical-align: bottom;
  padding-bottom: 5px;
  margin-right: 8px;
  margin-left: 1.1em;
}
.wrap.is-shortpixel-bulk-page header .top-buttons {
  margin-right: 4em;
}
.wrap.is-shortpixel-bulk-page header .top-buttons a, .wrap.is-shortpixel-bulk-page header .top-buttons button {
  color: #fff;
  background: #1ABDCA;
  padding: 10px 12px;
  border: 0;
  margin: 4px;
  font-family: "Roboto";
  font-weight: 700;
  border-radius: 6px;
  text-decoration: none;
  transition: all 0.1s ease-in-out;
  box-shadow: none;
}
.wrap.is-shortpixel-bulk-page header .top-buttons a:hover, .wrap.is-shortpixel-bulk-page header .top-buttons button:hover {
  background: rgb(50.3421052632, 215.4868421053, 228.6578947368);
}
.wrap.is-shortpixel-bulk-page header .top-buttons a i.switch, .wrap.is-shortpixel-bulk-page header .top-buttons button i.switch {
  width: 18px;
  height: 12px;
}
.wrap.is-shortpixel-bulk-page header .top-buttons a i.notifications, .wrap.is-shortpixel-bulk-page header .top-buttons button i.notifications {
  width: 14px;
  height: 14px;
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(251deg) brightness(108%) contrast(108%);
}
.wrap.is-shortpixel-bulk-page header .top-buttons .header-button i {
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(20%) hue-rotate(283deg) brightness(105%) contrast(105%);
  margin-right: 4px;
  height: 15px;
  width: 15px;
  vertical-align: text-top;
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon {
  width: 25px;
  height: 25px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: cover;
  line-height: 25px;
  vertical-align: middle;
  margin-right: 4px;
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.rotate_right {
  rotate: -90deg;
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.ai {
  background-image: url("../images/icon/ai.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.alert {
  background-image: url("../images/icon/alert.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.arrow-right {
  background-image: url("../images/icon/arrow-right.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.arrows-left-right {
  background-image: url("../images/icon/arrows-left-right.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.arrows-up-down {
  background-image: url("../images/icon/arrows-up-down.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.box-archive {
  background-image: url("../images/icon/box-archive.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.bulk {
  background-image: url("../images/icon/bulk.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.chevron {
  background-image: url("../images/icon/chevron.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.close {
  background-image: url("../images/icon/close.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.cart {
  background-image: url("../images/icon/cart.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.dashboard {
  background-image: url("../images/icon/dashboard.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.debug {
  background-image: url("../images/icon/debug.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.delivery {
  background-image: url("../images/icon/delivery.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.exclusions {
  background-image: url("../images/icon/exclusions.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.edit {
  background-image: url("../images/icon/edit.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.eye {
  background-image: url("../images/icon/eye.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.file {
  background-image: url("../images/icon/file.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.fix {
  background-image: url("../images/icon/fix.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.feedback {
  background-image: url("../images/icon/feedback.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.help {
  background-image: url("../images/icon/help.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.integrations {
  background-image: url("../images/icon/integrations.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.optimization {
  background-image: url("../images/icon/optimization.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.processing {
  background-image: url("../images/icon/processing.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.no-ai {
  background-image: url("../images/icon/no-ai.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.notifications {
  background-image: url("../images/icon/notifications.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.refresh {
  background-image: url("../images/icon/refresh.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.robo {
  background-image: url("../images/icon/robo.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.rocket {
  background-image: url("../images/icon/rocket.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.tools {
  background-image: url("../images/icon/tools.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.trash {
  background-image: url("../images/icon/trash.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.webp_avif {
  background-image: url("../images/icon/webp_avif.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.photo {
  background-image: url("../images/icon/photo.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.ok {
  background-image: url("../images/icon/ok.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.warning {
  background-image: url("../images/icon/warning.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.key {
  background-image: url("../images/icon/key.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.save {
  background-image: url("../images/icon/save.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.switch {
  background-image: url("../images/icon/switch.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.help-circle {
  background-image: url("../images/icon/help-circle.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.user {
  background-image: url("../images/icon/user.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-icon.shortpixel {
  background-image: url("../images/icon/shortpixel.svg");
}
.wrap.is-shortpixel-bulk-page .shortpixel-illustration {
  display: inline-block;
  background-repeat: no-repeat;
  background-size: cover;
  width: 50px;
  height: 50px;
}
.wrap.is-shortpixel-bulk-page .shortpixel-illustration.wizzard-done {
  background-image: url("../images/illustration/wizzard-done.svg");
  width: 70px;
  height: 70px;
}
.wrap.is-shortpixel-bulk-page .shortpixel-illustration.cocktail {
  background-image: url("../images/illustration/cocktail.svg");
  height: 104px;
  width: 60px;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard {
  max-width: 95%;
  margin: 20px auto;
  border-radius: 16px;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard h3.heading {
  border: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-wrapper {
  text-align: center;
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-wrapper .dashboard-image {
  z-index: 1;
  position: absolute;
  left: 0;
  right: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-wrapper button {
  color: #ffffff;
  background-color: #1ABDCA;
  border-radius: 6px;
  width: 250px;
  height: 65px;
  font-weight: 700;
  letter-spacing: 0.3px;
  border: none;
  transition: all 0.1s ease-in-out;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-wrapper button:hover {
  background: rgb(50.3421052632, 215.4868421053, 228.6578947368);
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-wrapper button:disabled {
  color: #ccc;
  background: #0e5358;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-wrapper button span {
  margin-right: 16px;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-welcome {
  background-color: #d1f5fd;
  border-radius: 16px 16px 0 0;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .bulk-welcome h3 {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  padding-top: 16px;
  margin-top: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-text {
  margin: 0 auto;
  width: 600px;
}
@media (max-width: 768px) {
  .wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-text {
    width: 90%;
  }
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-text p {
  line-height: 25px;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log {
  display: table;
  width: 80%;
  margin: 0 auto;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log h3 {
  display: table-caption;
  text-align: center;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log > div {
  display: table-row;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log > div > span {
  display: table-cell;
  padding: 8px 8px 8px 0;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log .head span {
  font-weight: 700;
  padding-left: 15px;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log .data span {
  border-bottom: 1px solid #EBF5FF;
  border-top: 1px solid #EBF5FF;
  font-size: 15px;
  padding-left: 10px;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log .data span:first-child {
  border-left: 1px solid #EBF5FF;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log .data span:last-child {
  border-right: 1px solid #EBF5FF;
}
.wrap.is-shortpixel-bulk-page section.panel.dashboard .dashboard-log .data span.date {
  color: #1FBEC9;
  background: url("../img/bulk/checkmark.svg") left center no-repeat;
  padding-left: 29px;
}
.wrap.is-shortpixel-bulk-page h3.heading {
  margin: 15px 0 15px 0px;
  padding-left: 15px;
  padding-bottom: 12px;
  font-size: 20px;
  font-weight: 400;
  line-height: 42px;
  border-bottom: 1px solid #ccc;
  position: relative;
}
.wrap.is-shortpixel-bulk-page h3.heading span {
  vertical-align: top;
  margin-right: 20px;
}
.wrap.is-shortpixel-bulk-page i {
  font-style: normal;
}
.wrap.is-shortpixel-bulk-page p.description {
  font-size: 14px;
  margin: 15px 0 15px 15px;
}
.wrap.is-shortpixel-bulk-page .button {
  display: flex;
  align-items: center;
  justify-content: center;
  float: left;
  margin-right: 8px;
  padding: 8px 16px;
  background: #F5F5F5;
  color: #1cbecb;
  letter-spacing: 0.3px;
  font-weight: 700;
  font-size: 14px;
  border-radius: 6px;
  border: none;
  transition: all 0.1s ease-in-out;
  min-height: 44px;
}
.wrap.is-shortpixel-bulk-page .button:hover {
  background: #1cbecb;
  color: #fff;
}
.wrap.is-shortpixel-bulk-page .button:focus {
  background: #1cbecb;
}
.wrap.is-shortpixel-bulk-page .button .dashicons {
  font-size: 26px;
  height: 26px;
  margin-right: 8px;
}
.wrap.is-shortpixel-bulk-page .button p {
  margin: 0;
  display: inline;
  font-size: 14px;
  vertical-align: bottom;
}
.wrap.is-shortpixel-bulk-page .button-primary {
  background: #1ABDCA;
  color: #fff;
  border-radius: 6px;
  border: none;
  transition: all 0.1s ease-in-out;
}
.wrap.is-shortpixel-bulk-page .button-primary:hover {
  background: rgb(50.3421052632, 215.4868421053, 228.6578947368);
}
.wrap.is-shortpixel-bulk-page nav {
  margin: 45px auto;
  width: 80%;
  min-height: 50px;
}
.wrap.is-shortpixel-bulk-page nav .button-primary {
  margin-left: 15px;
}
.wrap.is-shortpixel-bulk-page .kbinfo a {
  text-decoration: none;
}
.wrap.is-shortpixel-bulk-page .hidden {
  display: none !important;
}
.wrap.is-shortpixel-bulk-page .dashicons.spin {
  animation: dashicons-spin 1s infinite;
  animation-timing-function: linear;
}
@keyframes dashicons-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.wrap.is-shortpixel-bulk-page .loading-icon {
  width: 50%;
  height: 50%;
}
.wrap.is-shortpixel-bulk-page .bulk-modal {
  position: absolute;
  padding: 0;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  width: auto;
  box-sizing: border-box;
  box-shadow: rgba(0, 0, 0, 0.5) 4px 4px 20px;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .close {
  position: absolute;
  right: 10px;
  top: 10px;
  color: white;
  font-size: 26px;
  cursor: pointer;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .title {
  margin: 0;
  padding: 12px;
  background: #1FB5BF;
  background: linear-gradient(90deg, #0f6a7d 0%, rgb(15, 106, 125) 0%, #1cbecb 100%);
  width: 100%;
  font-size: 16px;
  color: #ffffff;
  box-sizing: border-box;
  height: 40px;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content {
  padding: 12px;
  box-sizing: border-box;
  height: 90%;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content .table-wrapper {
  display: table;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content .table-wrapper .heading {
  font-weight: 700;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content .table-wrapper .heading > span {
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ddd;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content .table-wrapper > div {
  display: table-row;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content .table-wrapper > div > span {
  display: table-cell;
  padding: 4px 8px;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content .table-wrapper > div > span:first-child {
  white-space: nowrap;
}
.wrap.is-shortpixel-bulk-page .bulk-modal .content a {
  text-decoration: none;
  margin-left: 8px;
}
.wrap.is-shortpixel-bulk-page .screen-wrapper {
  width: 100%;
  min-height: 80vh;
  position: relative;
  margin-top: 20px;
}
.wrap.is-shortpixel-bulk-page .screen-wrapper .shortpixel-bulk-loader {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #fff;
  transition: opacity 1s linear;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  padding: 25px 0 15px 0;
  width: 60%;
  border-radius: 10px;
  margin: 0 auto;
}
.wrap.is-shortpixel-bulk-page .screen-wrapper .shortpixel-bulk-loader[data-status=loading] {
  z-index: 100;
  visibility: visible;
  opacity: 0.9;
  position: absolute;
  width: 100%;
}
.wrap.is-shortpixel-bulk-page .screen-wrapper .shortpixel-bulk-loader .loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  justify-content: center;
}
.wrap.is-shortpixel-bulk-page .screen-wrapper .shortpixel-bulk-loader .loader span h2 {
  color: #555;
}
.wrap.is-shortpixel-bulk-page .screen-wrapper .shortpixel-bulk-loader .loader .svg-spinner .svg-loader {
  width: 200px;
  height: 50px;
}
.wrap.is-shortpixel-bulk-page .panel {
  width: 100%;
  position: absolute;
  display: none;
  z-index: 0;
  visibility: hidden;
  opacity: 0;
  transition: opacity 500ms ease-in;
  margin: 15px 0;
  padding-bottom: 35px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 1px 0px 4px 0px rgba(0, 0, 0, 0.12);
}
.wrap.is-shortpixel-bulk-page .panel.active {
  visibility: visible;
  opacity: 1;
  z-index: 5;
  position: relative;
  width: 95%;
  margin: auto;
}
.wrap.is-shortpixel-bulk-page .panel .panel-container {
  z-index: 5;
  display: inline-block;
  width: 100%;
}
.wrap.is-shortpixel-bulk-page .panel .error h3 {
  margin-left: 12px;
}
.wrap.is-shortpixel-bulk-page .panel .error p {
  margin-left: 12px;
  color: #ff0000;
}
.wrap.is-shortpixel-bulk-page .panel .error p.text {
  color: #000;
}
.wrap.is-shortpixel-bulk-page .panel .errorbox {
  display: block;
  transition: 1s all;
  opacity: 0;
  display: none;
  right: 10px;
  top: 4px;
  padding: 8px;
  height: 100%;
  border: 1px solid #000;
  width: 80%;
  margin: 30px auto;
  background: rgba(255, 255, 255, 0.9);
  line-height: 22px;
  max-height: 300px;
  overflow-y: scroll;
}
.wrap.is-shortpixel-bulk-page .panel .errorbox .fatal {
  color: #ff0000;
}
.wrap.is-shortpixel-bulk-page .processor-paused {
  display: none;
  margin: 25px auto;
  width: 95%;
  background: rgb(31, 190, 201);
  border: 1px solid #ccc;
  font-size: 26px;
  line-height: 26px;
  padding: 26px 0;
  text-align: center;
  z-index: 10;
  border-radius: 16px;
  cursor: pointer;
  transition: all 900ms;
}
.wrap.is-shortpixel-bulk-page .processor-paused:hover {
  background: rgb(27.5926724138, 169.1163793103, 178.9073275862);
}
.wrap.is-shortpixel-bulk-page .processor-paused .dashicons {
  margin-right: 6px;
}
.wrap.is-shortpixel-bulk-page .processor-paused .dashicons::before {
  width: 26px;
  height: 26px;
  font-size: 26px;
}
.wrap.is-shortpixel-bulk-page .processor-paused .dashicons:hover::before {
  content: "\f522";
}
.wrap.is-shortpixel-bulk-page .processor-overquota {
  display: none;
  background: #ff0000;
  line-height: 26px;
  padding: 20px 0;
  border-radius: 16px;
  margin: 25px auto;
  width: 95%;
  text-align: center;
}
.wrap.is-shortpixel-bulk-page .processor-overquota h3 {
  font-size: 26px;
  color: #fff;
  margin: 10px 0;
}
.wrap.is-shortpixel-bulk-page .processor-overquota p a {
  font-size: 16px;
  color: #fff;
  text-decoration: none;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar {
  margin: 30px 0;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex {
  display: flex;
  flex-direction: row;
  width: 90%;
  justify-content: space-around;
  margin: 0 auto;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex > div {
  display: flex;
  align-items: center;
  position: relative;
  flex-direction: column;
  flex-grow: 1;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex > div.result .line {
  background: linear-gradient(90deg, rgb(204, 204, 204) 0%, rgb(204, 204, 204) 49.9%, rgb(255, 255, 255) 51%, rgb(255, 255, 255) 100%);
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex > div.select .line {
  width: 50%;
  left: 50%;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex .line {
  background: #ccc;
  height: 2px;
  width: 100%;
  box-sizing: border-box;
  display: inline-block;
  position: absolute;
  top: 22px;
  z-index: 1;
  transition: color 1s ease-in;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex .step {
  border: 2px solid #ccc;
  border-radius: 50%;
  height: 45px;
  font-size: 28px;
  font-weight: 700;
  color: #ccc;
  width: 45px;
  line-height: 35px;
  text-align: center;
  box-sizing: border-box;
  display: inline-block;
  z-index: 2;
  background: #fff;
  transition: color 1s ease-in;
  padding-top: 2px;
}
.wrap.is-shortpixel-bulk-page section.spio-progressbar .flex .text {
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  color: #ccc;
  margin-top: 8px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .spio-progressbar .select .line {
  background: linear-gradient(90deg, rgb(31, 190, 201) 0%, rgb(31, 190, 201) 49.9%, rgb(204, 204, 204) 50%, rgb(204, 204, 204) 100%);
}
.wrap.is-shortpixel-bulk-page section.panel.selection .spio-progressbar .select .text, .wrap.is-shortpixel-bulk-page section.panel.selection .spio-progressbar .select .step {
  color: #EC2C25;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .spio-progressbar .select .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper, .wrap.is-shortpixel-bulk-page section.panel.selection .interface.wrapper {
  opacity: 0;
  height: 0;
  transition: opacity 100ms ease-in;
}
.wrap.is-shortpixel-bulk-page section.panel.selection[data-status=loading] .load.wrapper {
  opacity: 1;
  height: auto;
  padding-bottom: 45px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection[data-status=loaded] .interface.wrapper {
  opacity: 1;
  height: auto;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading {
  display: flex;
  color: #ccc;
  font-weight: 700;
  align-items: center;
  width: 90%;
  margin: auto;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading > span {
  height: 65px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading > span p {
  margin: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading span:first-child {
  margin-right: 20px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading img {
  animation: cssload-spin 5000ms infinite linear;
  -o-animation: cssload-spin 5000ms infinite linear;
  -ms-animation: cssload-spin 5000ms infinite linear;
  -webkit-animation: cssload-spin 5000ms infinite linear;
  -moz-animation: cssload-spin 5000ms infinite linear;
}
@keyframes loading-spin {
  100% {
    transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-o-keyframes loading-spin {
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-ms-keyframes loading-spin {
  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes loading-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes loading-spin {
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading.skip {
  margin: 30px auto;
  padding-left: 90px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .load.wrapper .loading.overlimit {
  display: none;
  padding: 6px 8px;
  border: 1px solid #dba617;
  color: #646970;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .option-block {
  margin: 0 auto 35px;
  width: 80%;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .option-block h2 {
  font-size: 22px;
  color: #555;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .option-block p {
  margin-left: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup .switch_button {
  display: inline-block;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup h4 {
  font-size: 14px;
  font-weight: 700;
  display: inline-block;
  margin-left: 25px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup .option {
  margin-left: 85px;
  line-height: 26px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup .option label {
  width: 200px;
  display: inline-block;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup .option .number {
  font-weight: 700;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup .option.warning {
  border-left: 4px solid #dba617;
  padding-left: 8px;
  color: #000;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .optiongroup .new {
  color: #ff0000;
  font-weight: 500;
  margin-left: 2px;
}
.wrap.is-shortpixel-bulk-page section.panel.selection .count_limited {
  color: #ff0000;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .select .line {
  background: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .select .text, .wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .select .step {
  color: #ccc;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .select .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .summary .line {
  background: linear-gradient(90deg, rgb(31, 190, 201) 0%, rgb(31, 190, 201) 49.9%, rgb(204, 204, 204) 50%, rgb(204, 204, 204) 100%);
}
.wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .summary .text, .wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .summary .step {
  color: #EC2C25;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .spio-progressbar .summary .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list {
  background-color: #F6F9FF;
  width: 80%;
  margin: 0 auto;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list h3 {
  font-size: 18px;
  font-weight: 400;
  padding: 26px 0;
  color: #555D66;
  padding-left: 35px;
  border-bottom: 1px solid #DFEAFF;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list h3 span {
  float: right;
  margin-right: 35px;
  margin-top: -15px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper h4 {
  font-size: 14px;
  font-weight: 700;
  padding-left: 80px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper h4 .dashicons {
  color: #1FBEC9;
  margin-right: 10px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper .list-table {
  display: table;
  width: 90%;
  margin: 0 auto;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper .list-table > div {
  display: table-row;
  background-color: #F0F5FF;
  font-size: 16px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper .list-table > div.filetypes {
  background: #F5F9FF;
  color: #aaa;
  font-size: 12px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper .list-table > div.filetypes > span {
  border: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .summary-list .section-wrapper .list-table > div > span {
  display: table-cell;
  border: 1px solid #DFEAFF;
  width: 50%;
  padding: 15px 0 15px 15px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .totals {
  text-align: center;
  font-size: 25px;
  padding: 35px 0;
  background: #F6F9FF;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .totals .number {
  margin-left: 35px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits {
  display: table;
  width: 80%;
  margin: 35px auto;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits > p {
  display: table-row;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits > p > span {
  display: table-cell;
  padding: 10px 0;
  font-size: 14px;
  vertical-align: middle;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits > p > span:first-child {
  font-weight: 700;
  width: 40%;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits > p > span:nth-child(2n) {
  width: 30%;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits > p > span:nth-child(3n) {
  text-align: right;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits > p > span .button {
  float: right;
  margin-right: 0;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .credits .heading span {
  font-size: 18px;
  font-weight: 700;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .over-quota, .wrap.is-shortpixel-bulk-page section.panel.summary .no-images {
  border: 1px solid #ff0000;
  padding: 16px;
  width: 80%;
  margin: 30px auto;
  display: flex;
  align-items: center;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .over-quota > span, .wrap.is-shortpixel-bulk-page section.panel.summary .no-images > span {
  margin-right: 20px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .over-quota p, .wrap.is-shortpixel-bulk-page section.panel.summary .no-images p {
  max-width: 550px;
  font-size: 14px;
  line-height: 21px;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .over-quota p .red, .wrap.is-shortpixel-bulk-page section.panel.summary .no-images p .red {
  color: #ff0000;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .over-quota button, .wrap.is-shortpixel-bulk-page section.panel.summary .no-images button {
  background: #F5F5F5;
  color: #1FBEC9;
  border: 1px solid #1FBEC9;
  border-radius: 3px;
  box-shadow: 0px 1px 1px rgba(171, 170, 170, 0.3);
  margin: 16px 0;
}
.wrap.is-shortpixel-bulk-page section.panel.summary .no-images {
  font-size: 14px;
  padding: 16px;
  font-weight: 500;
}
.wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .select .line, .wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .summary .line, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .line, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .line {
  background: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .select .text, .wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .select .step, .wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .summary .text, .wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .summary .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .step {
  color: #ccc;
}
.wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .select .step, .wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .summary .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .process .line, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .line {
  background: linear-gradient(90deg, rgb(31, 190, 201) 0%, rgb(31, 190, 201) 49.9%, rgb(204, 204, 204) 50%, rgb(204, 204, 204) 100%);
}
.wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .process .text, .wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .process .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .step {
  color: #EC2C25;
}
.wrap.is-shortpixel-bulk-page section.panel.process .spio-progressbar .process .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.process .average-optimization, .wrap.is-shortpixel-bulk-page section.panel.finished .average-optimization {
  position: absolute;
  top: 0px;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 2;
  background: rgba(255, 255, 255, 0.4);
}
.wrap.is-shortpixel-bulk-page section.panel.process .average-optimization p, .wrap.is-shortpixel-bulk-page section.panel.finished .average-optimization p {
  margin: 0;
  font-size: 13px;
  position: absolute;
  bottom: 45px;
  left: -15px;
  padding: 6px 10px;
  color: #fff;
  background: rgba(15, 106, 125, 0.5);
  font-weight: 700;
  border-radius: 3px;
  white-space: nowrap;
}
.wrap.is-shortpixel-bulk-page section.panel.process .average-optimization .opt-circle-average, .wrap.is-shortpixel-bulk-page section.panel.finished .average-optimization .opt-circle-average {
  width: 150px;
  height: 150px;
  float: right;
}
.wrap.is-shortpixel-bulk-page section.panel.process .average-optimization .opt-circle-average .trail, .wrap.is-shortpixel-bulk-page section.panel.finished .average-optimization .opt-circle-average .trail {
  stroke: #ddd;
}
.wrap.is-shortpixel-bulk-page section.panel.process .average-optimization .opt-circle-average .path, .wrap.is-shortpixel-bulk-page section.panel.finished .average-optimization .opt-circle-average .path {
  stroke: #1cbecb;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease 0s;
}
.wrap.is-shortpixel-bulk-page section.panel.process .average-optimization .opt-circle-average .text, .wrap.is-shortpixel-bulk-page section.panel.finished .average-optimization .opt-circle-average .text {
  fill: #1FBEC9;
  font-size: 28px;
  font-weight: 700;
  dominant-baseline: middle;
  text-anchor: middle;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary {
  display: table;
  width: 80%;
  margin: 30px auto;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary > div, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary > div {
  display: table-row;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary > div > span, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary > div > span {
  display: table-cell;
  padding: 16px;
  width: 33%;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .heading, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .heading {
  background: #F6F9FF;
  border: 1px solid #EBF5FF;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .heading .special-op, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .heading .special-op {
  color: #EC2C25;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .line-progressbar, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .line-progressbar {
  width: 100px;
  height: 20px;
  position: relative;
  background: #ccc;
  display: inline-block;
  vertical-align: middle;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .line-progressbar .done-text, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .line-progressbar .done-text {
  position: absolute;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  top: 0;
  bottom: 0;
  padding: 5px 4px;
  line-height: 10px;
  z-index: 3;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .line-progressbar .done, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .line-progressbar .done {
  position: absolute;
  z-index: 2;
  left: 0;
  width: 10%;
  top: 0;
  bottom: 0;
  background: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .line-progressbar .finished-paragraph, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .line-progressbar .finished-paragraph {
  font-size: 14px;
  line-height: 25px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .line-progressbar label input[type=checkbox], .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .line-progressbar label input[type=checkbox] {
  margin-right: 8px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .bulk-summary .display-error-box, .wrap.is-shortpixel-bulk-page section.panel.finished .bulk-summary .display-error-box {
  float: right;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section {
  background: #F6F9FF;
  border: 1px solid #DFEAFF;
  width: 80%;
  margin: 15px auto;
  position: relative;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .title, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .title {
  position: absolute;
  top: 0;
  width: 300px;
  background: rgba(15, 106, 125, 0.3137254902);
  background: linear-gradient(90deg, rgb(15, 106, 125) 0%, rgb(15, 106, 125) 0%, rgba(0, 212, 255, 0) 100%);
  color: #fff;
  font-size: 20px;
  padding: 15px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .image-preview-line, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .image-preview-line {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .image-preview-line [data-result=filename], .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .image-preview-line [data-result=filename] {
  overflow: hidden;
  font-weight: 700;
  margin-top: -12px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .image-preview-line .opt-circle-image, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .image-preview-line .opt-circle-image {
  width: 55px;
  height: 55px;
  float: right;
  margin-top: 12px;
  margin-right: 5%;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .image-preview-line .opt-circle-image .trail, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .image-preview-line .opt-circle-image .trail {
  stroke: #ddd;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .image-preview-line .opt-circle-image .path, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .image-preview-line .opt-circle-image .path {
  stroke: #1FBEC9;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.5s ease 0s;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .image-preview-line .opt-circle-image .text, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .image-preview-line .opt-circle-image .text {
  fill: #1FBEC9;
  font-size: 28px;
  font-weight: 700;
  dominant-baseline: middle;
  text-anchor: middle;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper {
  width: 100%;
  overflow: hidden;
  margin: 25px 0 40px 0;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .slide-mask, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .slide-mask {
  flex-wrap: nowrap;
  display: flex;
  justify-content: flex-start;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .preview-image, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .preview-image {
  transition: 0.5s all linear;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 0;
  width: 100%;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .current.preview-image, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .current.preview-image {
  opacity: 1;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .old.preview-image, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .old.preview-image {
  display: none;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image {
  max-width: 45%;
  max-height: 600px;
  overflow: hidden;
  position: relative;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image img, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image img {
  width: 100%;
  height: auto;
  object-fit: cover;
  z-index: 10;
  position: relative;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image img[src*="placeholder.svg"], .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image img[src*="placeholder.svg"] {
  background-color: #ebedf1;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image img[src*="placeholder.svg"]:not(.notempty), .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image img[src*="placeholder.svg"]:not(.notempty) {
  z-index: 1;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image .svg-loader, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image .svg-loader {
  width: 100%;
  height: 70px;
  position: absolute;
  top: calc(50% - 35px);
  left: 0;
  z-index: 5;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image p, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image p {
  position: absolute;
  top: 0;
  z-index: 10;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  padding: 4px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image.source, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image.source {
  margin-right: 8px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image.result, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image.result {
  margin-left: 8px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .preview-wrapper .image.result p, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .preview-wrapper .image.result p {
  font-weight: 700;
  font-size: 14px;
}
.wrap.is-shortpixel-bulk-page section.panel.process .image-preview-section .ai-preview-wrapper .ai-preview-data strong, .wrap.is-shortpixel-bulk-page section.panel.finished .image-preview-section .ai-preview-wrapper .ai-preview-data strong {
  text-transform: capitalize;
}
.wrap.is-shortpixel-bulk-page section.panel.process nav, .wrap.is-shortpixel-bulk-page section.panel.finished nav {
  width: 80%;
  margin-top: 35px;
}
.wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .line, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .line, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .line {
  background: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .step {
  color: #ccc;
}
.wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .select .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .summary .step, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .process .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .result .line {
  background: linear-gradient(90deg, rgb(31, 190, 201) 0%, rgb(31, 190, 201) 49.9%, rgb(204, 204, 204) 50%, rgb(204, 204, 204) 100%);
}
.wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .result .text, .wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .result .step {
  color: #EC2C25;
}
.wrap.is-shortpixel-bulk-page section.panel.finished .spio-progressbar .result .step {
  border-color: #1FBEC9;
}
.wrap.is-shortpixel-bulk-page section.panel.finished nav {
  text-align: center;
}
.wrap.is-shortpixel-bulk-page section.panel.finished nav button.finish {
  padding: 12px 60px;
  border-radius: 3px;
  background: #1FBEC9;
  font-size: 16px;
  color: #fff;
  transition: all 0.5s ease;
}
.wrap.is-shortpixel-bulk-page section.panel.finished nav button.finish:hover {
  background: rgb(36.2392241379, 209.2887931034, 221.2607758621);
}
.wrap.is-shortpixel-bulk-page .part-debug {
  background: #000;
  color: #fff;
  width: 100%;
  min-height: 150px;
  max-height: 550px;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper {
  margin: 0 auto;
  max-width: 80%;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper h4 {
  color: #ff0000;
  font-weight: 700;
  font-size: 20px;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper h4.warning {
  float: left;
  margin: 25px;
  min-height: 50px;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .warning {
  color: #ff0000;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup {
  clear: both;
  margin-left: 130px;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup .switch_button {
  display: inline-block;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup h4 {
  font-size: 14px;
  font-weight: 400;
  display: inline-block;
  margin-left: 25px;
  color: #000;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup .option {
  margin-left: 85px;
  line-height: 26px;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup .option label {
  width: 200px;
  display: inline-block;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup .option .number {
  font-weight: 700;
}
.wrap.is-shortpixel-bulk-page .bulk-special-wrapper .optiongroup.warning {
  font-weight: 700;
  color: #ff0000;
}

/*# sourceMappingURL=shortpixel-bulk.css.map */
