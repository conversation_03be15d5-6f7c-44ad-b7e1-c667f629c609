# Translation of RocketGenius - Gravity Forms Advanced Post Creation in Swedish
# This file is distributed under the same license as the RocketGenius - Gravity Forms Advanced Post Creation package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-03-19 12:52:48+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-rc.1\n"
"Language: sv_SE\n"
"Project-Id-Version: RocketGenius - Gravity Forms Advanced Post Creation\n"

#: includes/class-post-update-handler.php:150
msgid "Post #%d updated."
msgstr "Inlägg #%d uppdaterades."

#: includes/class-post-update-handler.php:144
msgid "Error updating post #%d: %s"
msgstr "Fel vid uppdatering av inlägg #%d: %s"

#: class-gf-advancedpostcreation.php:1918
msgid "Post created: %d."
msgstr "Inlägg skapat: %d."

#: class-gf-advancedpostcreation.php:644
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create the post content."
msgstr "När den är aktiverad kommer automatisk formatering att infoga styckebrytningar automatiskt. Inaktivera automatisk formatering när du använder HTML för att skapa inläggets innehåll."

#: class-gf-advancedpostcreation.php:643
msgid "Disable Auto-Formatting"
msgstr "Inaktivera automatisk formatering"

#: class-gf-advancedpostcreation.php:2748
msgid "Edit Post"
msgstr "Redigera inlägg"

#: class-gf-advancedpostcreation.php:2746
msgid "View Post"
msgstr "Visa inlägg"

#: class-gf-advancedpostcreation.php:2712
msgid "Created Posts"
msgstr "Skapade inlägg"

#: class-gf-advancedpostcreation.php:1735
#: class-gf-advancedpostcreation.php:1759
msgid "Not Available"
msgstr "Inte tillgängligt"

#: class-gf-advancedpostcreation.php:1729
#: class-gf-advancedpostcreation.php:1753
msgid "Not Selected"
msgstr "Inte valt"

#: class-gf-advancedpostcreation.php:1526
msgid "Assign terms to the post using a field value, an existing term or by adding a new term."
msgstr "Koppla termer till inlägget med hjälp av ett fältvärde, en befintlig term eller genom att lägga till en ny term."

#: class-gf-advancedpostcreation.php:1440
#: class-gf-advancedpostcreation.php:1445
#: class-gf-advancedpostcreation.php:1630
#: class-gf-advancedpostcreation.php:1635
msgid "Access denied."
msgstr "Åtkomst nekad."

#: class-gf-advancedpostcreation.php:1401
#: class-gf-advancedpostcreation.php:1455
msgid "Logged In User"
msgstr "Inloggad användare"

#: class-gf-advancedpostcreation.php:1375
msgid "Add New Custom Field Name"
msgstr "Lägg till nytt anpassat fältnamn"

#: class-gf-advancedpostcreation.php:1352
msgid "Select a Custom Field Name"
msgstr "Välj ett anpassat fältnamn"

#: class-gf-advancedpostcreation.php:1196
msgid "Custom Post Types"
msgstr "Anpassade inläggstyper"

#: class-gf-advancedpostcreation.php:1155
msgid "WordPress Post Types"
msgstr "WordPress inläggstyper"

#: class-gf-advancedpostcreation.php:1006
#: class-gf-advancedpostcreation.php:1675
msgid "Add New %s"
msgstr "Skapa ny %s"

#: class-gf-advancedpostcreation.php:992
msgid "Assign %s"
msgstr "Tilldela %s"

#: class-gf-advancedpostcreation.php:988
msgid "Field Value"
msgstr "Fältvärde"

#: class-gf-advancedpostcreation.php:984
msgid "Select an Option"
msgstr "Välj ett alternativ"

#: class-gf-advancedpostcreation.php:791
msgid "Taxonomies"
msgstr "Taxonomier"

#: class-gf-advancedpostcreation.php:774
msgid "Select file upload fields whose files should be uploaded to the media library and assigned to the post."
msgstr "Välj fält för filuppladdning vars filer ska laddas upp till mediabiblioteket och kopplas till inlägget."

#: class-gf-advancedpostcreation.php:769
msgid "Select File Upload Fields"
msgstr "Välj filuppladdningsfält"

#: class-gf-advancedpostcreation.php:765 class-gf-advancedpostcreation.php:773
msgid "Media Library"
msgstr "Mediabibliotek"

#: class-gf-advancedpostcreation.php:751
msgid "Select a file upload field to use for the post's featured image. Only file upload fields limited to a single file are permitted."
msgstr "Välj ett filuppladdningsfält som ska användas för inläggets utvalda bild. Endast filuppladdningsfält som är begränsade till en enda fil är tillåtna."

#: class-gf-advancedpostcreation.php:744 class-gf-advancedpostcreation.php:750
msgid "Featured Image"
msgstr "Utvald bild"

#: class-gf-advancedpostcreation.php:734
msgid "Select a File Upload Field"
msgstr "Välj fält för filuppladdning"

#: class-gf-advancedpostcreation.php:707
msgid "Excerpt"
msgstr "Utdrag"

#: class-gf-advancedpostcreation.php:684
msgid "Format"
msgstr "Format"

#: class-gf-advancedpostcreation.php:667
msgid "Map form values to post meta using an existing meta key or defining a new one."
msgstr "Koppla formulärvärden till inläggsmeta med hjälp av en befintlig metanyckel eller genom att definiera en ny."

#: class-gf-advancedpostcreation.php:662
msgid "Custom Field Value"
msgstr "Anpassat fältvärde"

#: class-gf-advancedpostcreation.php:656
msgid "Custom Field Name"
msgstr "Anpassat fältnamn"

#: class-gf-advancedpostcreation.php:651 class-gf-advancedpostcreation.php:666
msgid "Custom Fields"
msgstr "Anpassade fält"

#: class-gf-advancedpostcreation.php:630
msgid "Define the post's content. File upload field merge tags used within the post content will automatically have their files uploaded to the media library and associated with the post."
msgstr "Ange inläggets innehåll. Kopplingstaggar för filuppladdning som används inom inläggets innehåll gör automatiskt så att filerna att laddas upp till mediabiblioteket och taggen associeras med posten i fråga."

#: class-gf-advancedpostcreation.php:621
msgid "Content"
msgstr "Innehåll"

#: class-gf-advancedpostcreation.php:613
msgid "Title"
msgstr "Rubrik"

#: class-gf-advancedpostcreation.php:609 class-gf-advancedpostcreation.php:629
msgid "Post Content"
msgstr "Inläggsinnehåll"

#: class-gf-advancedpostcreation.php:601
msgid "Allow Trackbacks and Pingbacks"
msgstr "Tillåt trackbacks/pingbacks"

#: class-gf-advancedpostcreation.php:597
msgid "Allow Comments"
msgstr "Tillåt kommentarer"

#: class-gf-advancedpostcreation.php:593
msgid "Discussion"
msgstr "Diskussion"

#: class-gf-advancedpostcreation.php:587
msgid "Select the user to be assigned to the post."
msgstr "Välj vilken användare som ska stå som ägare av inlägget."

#: class-gf-advancedpostcreation.php:586
msgid "Post Author"
msgstr "Inläggsförfattare"

#: class-gf-advancedpostcreation.php:579
msgid "Author"
msgstr "Författare"

#: class-gf-advancedpostcreation.php:574
msgid "Define the post publish date. Custom date & time can be a specific date and time or a relative time (e.g. \"today\", \"next week\")."
msgstr "Ange inläggets publiceringsdatum. Anpassat datum och tid kan vara en specifik tidpunkt eller en relativ tidpunkt (t.ex. ”i dag”, \"nästa vecka”)."

#: class-gf-advancedpostcreation.php:573
msgid "Post Date"
msgstr "Inläggsdatum"

#: class-gf-advancedpostcreation.php:566
msgid "Date"
msgstr "Datum"

#: class-gf-advancedpostcreation.php:561
msgid "Select the status for the post. If published status is selected and the post date is in the future, it will automatically be changed to scheduled."
msgstr "Välj önskad status för inlägget. Om du väljer statusen ”publicerat” och publiceringsdatumet ligger i framtiden ändras statusen automatiskt till ”schemalagt”."

#: class-gf-advancedpostcreation.php:560
msgid "Post Status"
msgstr "Inläggsstatus"

#: class-gf-advancedpostcreation.php:553 class-gf-advancedpostcreation.php:1710
msgid "Status"
msgstr "Status"

#: class-gf-advancedpostcreation.php:548
msgid "Select one of the defined WordPress post types for the post."
msgstr "Välj någon av de inläggstyper som är definierade i WordPress för inlägget."

#: class-gf-advancedpostcreation.php:547 class-gf-advancedpostcreation.php:1709
msgid "Post Type"
msgstr "Inläggstyp"

#: class-gf-advancedpostcreation.php:540
msgid "Type"
msgstr "Typ"

#: class-gf-advancedpostcreation.php:535
msgid "Users will be required to enter the chosen post password to view the post content"
msgstr "Användare måste ange det valda lösenordet för inlägget för att kunna se dess innehåll"

#: class-gf-advancedpostcreation.php:534
msgid "Post Password"
msgstr "Lösenord för inlägget"

#: class-gf-advancedpostcreation.php:528
msgid "Password"
msgstr "Lösenord"

#: class-gf-advancedpostcreation.php:523
msgid "Visibility determines who can read the post."
msgstr "Synlighet avgör vilka som kan läsa inlägget."

#: class-gf-advancedpostcreation.php:522
msgid "Post Visibility"
msgstr "Inläggets synlighet"

#: class-gf-advancedpostcreation.php:516
msgid "Password Protected"
msgstr "Lösenordsskyddat"

#: class-gf-advancedpostcreation.php:512
msgid "Public"
msgstr "Offentlig"

#: class-gf-advancedpostcreation.php:504
msgid "Visibility"
msgstr "Synlighet"

#: class-gf-advancedpostcreation.php:500
msgid "Post Settings"
msgstr "Inläggsinställningar"

#: class-gf-advancedpostcreation.php:486
msgid "Custom Date & Time"
msgstr "Anpassat datum och tid"

#: class-gf-advancedpostcreation.php:482
msgid "Date & Time Fields"
msgstr "Fält för datum och tid"

#: class-gf-advancedpostcreation.php:478
msgid "Entry Date"
msgstr "Inläggsdatum"

#: class-gf-advancedpostcreation.php:434
msgid "When conditional logic is enabled, form submissions will only be created when the condition is met. When disabled, all form submissions will be created."
msgstr "När villkorsstyrd logik är aktiverad kommer formulär för insändning att skapas endast om villkoret är uppfyllt. När denna logik är inaktiv kommer alla inskickade formulär att skapas."

#: class-gf-advancedpostcreation.php:430
msgid "Create if"
msgstr "Skapa om"

#: class-gf-advancedpostcreation.php:429
msgid "Enable"
msgstr "Aktivera"

#: class-gf-advancedpostcreation.php:428 class-gf-advancedpostcreation.php:433
msgid "Conditional Logic"
msgstr "Villkorsstyrd logik"

#: class-gf-advancedpostcreation.php:413
msgid "Action"
msgstr "Åtgärd"

#: class-gf-advancedpostcreation.php:408
msgid "Enter a feed name to uniquely identify this setup."
msgstr "Ange ett flödesnamn som unikt identifierar denna konfiguration."

#: class-gf-advancedpostcreation.php:407 class-gf-advancedpostcreation.php:657
#: class-gf-advancedpostcreation.php:1708
msgid "Name"
msgstr "Namn"

#: class-gf-advancedpostcreation.php:401
msgid "Feed Name"
msgstr "Namn på flödet"

#: class-gf-advancedpostcreation.php:366
msgid "Select a User"
msgstr "Välj en användare"

#: class-gf-advancedpostcreation.php:248
msgid "Create post only when payment is received."
msgstr "Skapa inlägg först när betalning har tagit emot."

#. Author of the plugin
#: advancedpostcreation.php
msgid "Gravity Forms"
msgstr "Gravity Forms"

#. Description of the plugin
#: advancedpostcreation.php
msgid "Allows you to create new posts through Gravity Forms."
msgstr "Låter dig skapa nya inlägg med hjälp av Gravity Forms."

#. Plugin URI of the plugin
#. Author URI of the plugin
#: advancedpostcreation.php
msgid "https://gravityforms.com"
msgstr "https://gravityforms.com"

#. Plugin Name of the plugin
#: advancedpostcreation.php
msgid "Gravity Forms Advanced Post Creation Add-On"
msgstr "Gravity Forms utökning för avancerat skapande av inlägg"