# Translation of Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release) in Swedish
# This file is distributed under the same license as the Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-09-03 20:41:50+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: sv_SE\n"
"Project-Id-Version: Plugins - Yoast SEO &#8211; Advanced SEO with real-time guidance and built-in AI - Stable (latest release)\n"

#. translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s
#. expands to "Yoast SEO Premium".
#: src/presenters/admin/sidebar-presenter.php:74 js/dist/ai-consent.js:8
#: js/dist/ai-consent.js:9 js/dist/ai-generator.js:8 js/dist/ai-generator.js:9
#: js/dist/block-editor.js:23 js/dist/block-editor.js:24
#: js/dist/classic-editor.js:8 js/dist/classic-editor.js:9
#: js/dist/editor-modules.js:316 js/dist/editor-modules.js:317
#: js/dist/elementor.js:9 js/dist/elementor.js:10
#: js/dist/externals-components.js:189 js/dist/externals-components.js:190
#: js/dist/general-page.js:10 js/dist/general-page.js:11
#: js/dist/integrations-page.js:44 js/dist/integrations-page.js:45
#: js/dist/new-settings.js:10 js/dist/new-settings.js:11 js/dist/redirects.js:8
#: js/dist/redirects.js:9 js/dist/support.js:10 js/dist/support.js:11
msgid "%1$s%2$s %3$s"
msgstr "%1$s%2$s %3$s"

#: src/ai-consent/user-interface/ai-consent-integration.php:109
msgid "AI features"
msgstr "AI-funktioner"

#: src/plans/user-interface/plans-page-integration.php:119 js/dist/plans.js:7
msgid "Plans"
msgstr "Paket"

#. translators: 1,3,5: expand to opening paragraph tag, 2,4,6: expand to
#. opening paragraph tag.
#: src/llms-txt/user-interface/health-check/file-reports.php:64
msgid "%1$sYou have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file.%2$s%3$sIt looks like there is an llms.txt file already that wasn't created by Yoast, or the llms.txt file created by Yoast has been edited manually.%4$s%5$sWe don't want to overwrite this file's content, so if you want to let Yoast keep auto-generating the llms.txt file, you can manually delete the existing one. Otherwise, consider disabling the Yoast feature.%6$s"
msgstr "%1$sDu har aktiverat Yoasts llms.txt-funktion, men vi kunde inte skapa en llms.txt-fil.%2$s%3$sDet verkar som att det redan finns en llms.txt-fil som inte har skapats av Yoast, eller så har den llms.txt-fil som skapades av Yoast redigerats manuellt.%4$s%5$sVi vill inte skriva över innehållet i denna fil, så om du vill att Yoast ska fortsätta generera llms.txt-filen automatiskt kan du ta bort den befintliga filen manuellt. Annars kan du överväga att inaktivera Yoast-funktionen.%6$s"

#: src/llms-txt/application/file/file-failure-notification-presenter.php:49
#: src/llms-txt/user-interface/health-check/file-reports.php:86
msgid "You have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file, for unknown reasons."
msgstr "Du har aktiverat Yoasts llms.txt-funktion, men vi kunde inte skapa en llms.txt-fil av okänd anledning."

#. translators: 1,3: expand to opening paragraph tag, 2,4: expand to opening
#. paragraph tag.
#: src/llms-txt/user-interface/health-check/file-reports.php:77
msgid "%1$sYou have activated the Yoast llms.txt feature, but we couldn't generate an llms.txt file.%2$s%3$sIt looks like there aren't sufficient permissions on the web server's filesystem.%4$s"
msgstr "%1$sDu har aktiverat Yoasts llms.txt-funktion, men vi kunde inte skapa en llms.txt-fil.%2$s%3$sDet verkar som att det inte finns tillräckliga behörigheter på webbserverns filsystem.%4$s"

#: src/llms-txt/application/file/file-failure-notification-presenter.php:55
#: src/llms-txt/user-interface/health-check/file-reports.php:61
#: src/llms-txt/user-interface/health-check/file-reports.php:74
#: src/llms-txt/user-interface/health-check/file-reports.php:85
msgid "Your llms.txt file couldn't be auto-generated"
msgstr "Din llms.txt-fil kunde inte genereras automatiskt"

#. translators: %s: Yoast SEO.
#: src/llms-txt/user-interface/health-check/file-reports.php:40
msgid "%s keeps your llms.txt file up-to-date. This helps LLMs access and provide your site's information more easily."
msgstr "%s håller din llms.txt-fil uppdaterad. Detta hjälper LLM:er att få åtkomst till och tillhandahålla information från din webbplats på ett enklare sätt."

#. translators: %s: Yoast SEO.
#: src/llms-txt/user-interface/health-check/file-reports.php:34
msgid "Your llms.txt file is auto-generated by %s"
msgstr "Din llms.txt-fil genereras automatiskt av %s"

#: src/dashboard/user-interface/tracking/setup-steps-tracking-route.php:149
msgid "No valid parameters were passed."
msgstr "Inga giltiga parametrar angavs."

#: src/integrations/admin/link-count-columns-integration.php:147
msgid "Number of internal links linking to this post."
msgstr "Antal interna länkar som pekar på detta inlägg."

#: src/integrations/admin/link-count-columns-integration.php:139
msgid "Number of outgoing internal links in this post."
msgstr "Antal utgående interna länkar från detta inlägg."

#: inc/class-wpseo-rank.php:223 js/dist/externals/dashboardFrontend.js:4
msgid "Not analyzed"
msgstr "Ej analyserat"

#. translators: %s: Yoast SEO.
#: wp-seo-main.php:564
msgid "%s activation failed"
msgstr "Aktivering av %s misslyckades"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:42
msgid "%s is unable to create database tables"
msgstr "%s kan inte skapa databastabeller"

#: src/presenters/admin/sidebar-presenter.php:104
#: src/presenters/admin/sidebar-presenter.php:107 js/dist/ai-consent.js:10
#: js/dist/ai-generator.js:10 js/dist/block-editor.js:25
#: js/dist/classic-editor.js:10 js/dist/editor-modules.js:318
#: js/dist/elementor.js:11 js/dist/externals-components.js:191
#: js/dist/general-page.js:12 js/dist/integrations-page.js:46
#: js/dist/new-settings.js:12 js/dist/redirects.js:10 js/dist/support.js:12
msgid "Buy now"
msgstr "Köp nu"

#: admin/class-premium-upsell-admin-block.php:201
msgid "Upgrade now"
msgstr "Uppgradera nu"

#. translators: %1$s and %2$s expand to a span wrap to avoid linebreaks. %3$s
#. expands to "Yoast SEO Premium".
#: src/presenters/admin/sidebar-presenter.php:70 js/dist/ai-consent.js:10
#: js/dist/ai-generator.js:10 js/dist/block-editor.js:25
#: js/dist/classic-editor.js:10 js/dist/editor-modules.js:318
#: js/dist/elementor.js:11 js/dist/externals-components.js:191
#: js/dist/general-page.js:12 js/dist/integrations-page.js:46
#: js/dist/new-settings.js:12 js/dist/redirects.js:10 js/dist/support.js:12
msgid "%1$sBuy%2$s %3$s"
msgstr "%1$sKöp%2$s %3$s"

#: src/presenters/admin/sidebar-presenter.php:82 js/dist/ai-consent.js:9
#: js/dist/ai-generator.js:9 js/dist/block-editor.js:24
#: js/dist/classic-editor.js:9 js/dist/editor-modules.js:317
#: js/dist/elementor.js:10 js/dist/externals-components.js:190
#: js/dist/general-page.js:11 js/dist/integrations-page.js:45
#: js/dist/new-settings.js:11 js/dist/redirects.js:9 js/dist/support.js:11
msgid "If you were thinking about upgrading, now's the time! 30% OFF ends 3rd Dec 11am (CET)"
msgstr "Om du har tänkt på att uppgradera, så kan du göra det nu! 30 % RABATT till och med 3 december kl. 11:00 (CET)"

#: src/presenters/admin/sidebar-presenter.php:21 js/dist/ai-consent.js:10
#: js/dist/ai-generator.js:10 js/dist/block-editor.js:25
#: js/dist/block-editor.js:276 js/dist/classic-editor.js:10
#: js/dist/classic-editor.js:261 js/dist/editor-modules.js:174
#: js/dist/editor-modules.js:318 js/dist/elementor.js:11
#: js/dist/elementor.js:54 js/dist/externals-components.js:181
#: js/dist/externals-components.js:191 js/dist/general-page.js:12
#: js/dist/integrations-page.js:46 js/dist/new-settings.js:12
#: js/dist/redirects.js:10 js/dist/support.js:12
msgid "30% OFF - BLACK FRIDAY"
msgstr "30 % RABATT – BLACK FRIDAY"

#: src/integrations/admin/check-required-version.php:115
msgid "Required Yoast SEO version"
msgstr "Kräver Yoast SEO version"

#: src/integrations/admin/check-required-version.php:91
msgid "The package could not be installed because it's not supported by the currently installed Yoast SEO version."
msgstr "Paketet kunde inte installeras eftersom det inte stöds av den installerade versionen av Yoast SEO."

#. translators: 1: Current Yoast SEO version, 2: Version required by the
#. uploaded plugin.
#: src/integrations/admin/check-required-version.php:84
msgid "The Yoast SEO version on your site is %1$s, however the uploaded plugin requires %2$s."
msgstr "Yoast SEO på din webbplats är version %1$s, men utökningen du laddade upp kräver %2$s."

#: src/user-meta/framework/custom-meta/noindex-author.php:108
msgid "Do not allow search engines to show this author's archives in search results."
msgstr "Tillåt inte sökmotorer att visa denna författares arkiv i sökresultat."

#: admin/menu/class-base-menu.php:260 admin/menu/class-base-menu.php:264
msgid "Upgrades"
msgstr "Uppgraderingar"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "breadcrumbs"
msgstr "synliga sökvägar"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "internal linking"
msgstr "intern länkning"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block keyword"
msgid "site structure"
msgstr "webbplatsstruktur"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block title"
msgid "Yoast Breadcrumbs"
msgstr "Synliga sökvägar från Yoast SEO"

#: blocks/dynamic-blocks/breadcrumbs/block.json
msgctxt "block description"
msgid "Adds the Yoast SEO breadcrumbs to your template or content."
msgstr "Lägger till synlig sökväg från Yoast SEO i din mall eller ditt innehåll."

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block keyword"
msgid "FAQ"
msgstr "Vanliga frågor"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block keyword"
msgid "Frequently Asked Questions"
msgstr "Vanliga frågor"

#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "Schema"
msgstr "Schema"

#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "Structured Data"
msgstr "Strukturerade data"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block title"
msgid "Yoast How-to"
msgstr "Yoast-handledning"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "How-to"
msgstr "Handledning"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "How to"
msgstr "Handledning"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block title"
msgid "Yoast FAQ"
msgstr "Vanliga frågor om Yoast"

#: blocks/structured-data-blocks/faq/block.json
msgctxt "block description"
msgid "List your Frequently Asked Questions in an SEO-friendly way."
msgstr "Lista dina vanligaste frågor och svar på ett SEO-anpassat sätt."

#: blocks/dynamic-blocks/breadcrumbs/block.json
#: blocks/structured-data-blocks/faq/block.json
#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block keyword"
msgid "SEO"
msgstr "SEO"

#: admin/class-admin.php:315
#: src/user-meta/framework/additional-contactmethods/x.php:28
msgid "X username (without @)"
msgstr "X-användarnamn (utan @)"

#: src/presenters/admin/sidebar-presenter.php:116
msgid "30-day money back guarantee."
msgstr "30 dagars pengarna tillbaka-garanti."

#. translators: 1: PHP class name, 2: PHP variable name
#: inc/class-yoast-dynamic-rewrites.php:67
msgid "The %1$s class must not be instantiated before the %2$s global is set."
msgstr "Klassen %1$s får inte instansieras innan den globala variabeln %2$s har skapats."

#. translators: %s expands to Yoast WooCommerce SEO
#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to "Yoast WooCommerce SEO"
#. translators: %s expands to "Yoast SEO" Premium
#: admin/class-premium-upsell-admin-block.php:207
#: admin/class-premium-upsell-admin-block.php:209 js/dist/ai-consent.js:13
#: js/dist/ai-consent.js:14 js/dist/ai-generator.js:13
#: js/dist/ai-generator.js:14 js/dist/block-editor.js:28
#: js/dist/block-editor.js:29 js/dist/classic-editor.js:13
#: js/dist/classic-editor.js:14 js/dist/editor-modules.js:321
#: js/dist/editor-modules.js:322 js/dist/elementor.js:14
#: js/dist/elementor.js:15 js/dist/externals-components.js:194
#: js/dist/externals-components.js:195 js/dist/general-page.js:15
#: js/dist/general-page.js:16 js/dist/integrations-page.js:49
#: js/dist/integrations-page.js:50 js/dist/new-settings.js:15
#: js/dist/new-settings.js:16 js/dist/redirects.js:13 js/dist/redirects.js:14
#: js/dist/support.js:15 js/dist/support.js:16
msgid "Explore %s now!"
msgstr "Utforska %s nu!"

#: admin/watchers/class-slug-change-watcher.php:68
msgid "Search engines and other websites can still send traffic to your trashed content."
msgstr "Sökmotorer och andra webbplatser kan fortfarande skicka trafik till ditt borttagna innehåll."

#. translators: 1: Yoast SEO, 2: Link start tag to the Learn more link, 3: Link
#. closing tag.
#: src/presenters/admin/woocommerce-beta-editor-presenter.php:53
msgid "The %1$s interface is currently unavailable in the beta WooCommerce product editor. To resolve any issues, please disable the beta editor. %2$sLearn how to disable the beta WooCommerce product editor.%3$s"
msgstr "Gränssnittet %1$s är för närvarande inte tillgängligt i betaversionen av WooCommerce produktredigerare. För att lösa eventuella problem, bör du först inaktivera beta-redigeraren. %2$sLär dig hur du inaktiverar betaversionen av WooCommerce produktredigerare.%3$s"

#: src/presenters/admin/woocommerce-beta-editor-presenter.php:50
msgid "Compatibility issue: Yoast SEO is incompatible with the beta WooCommerce product editor."
msgstr "Kompatibilitetsproblem: Yoast SEO är inte kompatibelt med betaversionen av WooCommerce produktredigerare."

#: admin/class-premium-upsell-admin-block.php:83
#: admin/menu/class-base-menu.php:264 inc/class-wpseo-admin-bar-menu.php:597
#: js/dist/ai-consent.js:10 js/dist/ai-generator.js:10
#: js/dist/block-editor.js:25 js/dist/classic-editor.js:10
#: js/dist/editor-modules.js:318 js/dist/elementor.js:11
#: js/dist/externals-components.js:191 js/dist/general-page.js:12
#: js/dist/integrations-page.js:46 js/dist/new-settings.js:12
#: js/dist/redirects.js:10 js/dist/support.js:12
msgid "30% OFF"
msgstr "30&nbsp;% RABATT"

#: admin/class-premium-upsell-admin-block.php:82 js/dist/ai-consent.js:10
#: js/dist/ai-generator.js:10 js/dist/block-editor.js:25
#: js/dist/classic-editor.js:10 js/dist/editor-modules.js:318
#: js/dist/elementor.js:11 js/dist/externals-components.js:191
#: js/dist/general-page.js:12 js/dist/integrations-page.js:46
#: js/dist/new-settings.js:12 js/dist/redirects.js:10 js/dist/support.js:12
msgid "BLACK FRIDAY"
msgstr "BLACK FRIDAY"

#. translators: %1$s is a <br> tag.
#: inc/class-addon-manager.php:413
msgid "%1$s Now with 30%% Black Friday Discount!"
msgstr "%1$s Nu med 30&nbsp;%% rabatt under Black Friday!"

#: admin/views/class-yoast-feature-toggles.php:206 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "AI title & description generator"
msgstr "AI-generator för rubrik och beskrivning"

#: admin/views/class-yoast-feature-toggles.php:209
msgid "Use the power of Yoast AI to automatically generate compelling titles and descriptions for your posts and pages."
msgstr "Använd kraften hos Yoast AI till att automatiskt generera övertygande rubriker och beskrivningar för dina inlägg och sidor."

#. translators: %s expands to a unit of time (e.g. 1 day).
#. translators: %1$s and %2$s expand to units of time (e.g. 1 day).
#: src/integrations/blocks/structured-data-blocks.php:172
#: js/dist/how-to-block.js:9 js/dist/how-to-block.js:15
msgid "%1$s and %2$s"
msgstr "%1$s och %2$s"

#. translators: %s expands to a unit of time (e.g. 1 day).
#. translators: %1$s, %2$s and %3$s expand to units of time (e.g. 1 day).
#: src/integrations/blocks/structured-data-blocks.php:178
#: js/dist/how-to-block.js:10 js/dist/how-to-block.js:16
msgid "%1$s, %2$s and %3$s"
msgstr "%1$s, %2$s och %3$s"

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:66
msgid "Taxonomy is not new."
msgstr "Taxonomin är inte ny."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:37
msgid "Post type is not new."
msgstr "Inläggstypen är inte ny."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:73
msgid "Taxonomy is no longer new."
msgstr "Taxonomin är inte ny längre."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:43
msgid "Post type is no longer new."
msgstr "Inläggstypen är inte ny längre."

#. translators: 1: Opening tag of the link to the Search appearance settings
#. page, 2: Link closing tag.
#: src/content-type-visibility/application/content-type-visibility-watcher-actions.php:157
msgid "You've added a new type of content. We recommend that you review the corresponding %1$sSearch appearance settings%2$s."
msgstr "Du har lagt till en ny typ av innehåll. Vi rekommenderar att du granskar motsvarande %1$sInställningar för sökutseende%2$s."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:73
msgid "Error: Taxonomy was not removed from new_taxonomies list."
msgstr "Fel: Taxonomin togs inte bort från listan new_taxonomies."

#: src/content-type-visibility/application/content-type-visibility-dismiss-notifications.php:43
msgid "Error: Post type was not removed from new_post_types list."
msgstr "Fel: Inläggstypen togs inte bort från listan new_post_types."

#: src/integrations/support-integration.php:119 js/dist/support.js:18
msgid "Support"
msgstr "Support"

#: src/integrations/admin/crawl-settings-integration.php:163
#: js/dist/new-settings.js:33 js/dist/new-settings.js:178
msgid "Prevent Google AdsBot from crawling"
msgstr "Förhindra Google AdsBot från att genomsöka"

#. translators: %s expands to the inclusive language score
#: inc/class-wpseo-rank.php:239 inc/class-wpseo-rank.php:244
#: inc/class-wpseo-rank.php:249
msgid "Inclusive language: %s"
msgstr "Inkluderande språk: %s"

#. translators: %1$s expands to Yoast SEO, %2$s to Wincher
#: admin/class-wincher-dashboard-widget.php:58
msgid "%1$s / %2$s: Top Keyphrases"
msgstr "%1$s/%2$s: Populäraste nyckelordsfraser"

#: src/integrations/admin/background-indexing-integration.php:186
msgid "Every fifteen minutes"
msgstr "Var 15:e minut"

#: src/commands/index-command.php:174
msgid "Your WordPress environment is running on a non-production site. Indexables can only be created on production environments. Please check your `WP_ENVIRONMENT_TYPE` settings."
msgstr "Din WordPress-miljö körs i en icke-produktionsmiljö. ”Indexables” kan endast skapas i produktionsmiljöer. Kontrollera dina inställningar för ”WP_ENVIRONMENT_TYPE”."

#. translators: %s: expands to the post type
#: src/exceptions/indexable/post-type-not-built-exception.php:20
msgid "The post type %s could not be indexed because it does not meet indexing requirements."
msgstr "Inläggstypen %s kunde inte indexeras eftersom den inte uppfyller indexeringskraven."

#: src/integrations/academy-integration.php:111 js/dist/academy.js:2
msgid "Academy"
msgstr "Akademi"

#. translators: %1$s is the plugin name, %2$s and %3$s are a link.
#: inc/class-addon-manager.php:421
msgid "%1$s can't be updated because your product subscription is expired. %2$sRenew your product subscription%3$s to get updates again and use all the features of %1$s."
msgstr "%1$s kan inte uppdateras eftersom din produktprenumeration har gått ut. %2$sFörnya din produktprenumeration%3$s för att åter få uppdateringar och kunna använda alla funktioner i %1$s."

#. translators: %1$s expands to a strong tag, %2$s expands to the product name,
#. %3$s expands to a closing strong tag, %4$s expands to an a tag. %5$s expands
#. to MyYoast, %6$s expands to a closing a tag,  %7$s expands to the product
#. name
#: inc/class-addon-manager.php:522
msgid "%1$s %2$s isn't working as expected %3$s and you are not receiving updates or support! Make sure to %4$s activate your product subscription in %5$s%6$s to unlock all the features of %7$s."
msgstr "%1$s%2$s fungerar inte som det ska%3$s och du får inga uppdateringar eller support! Se till att %4$saktivera din produktprenumeration i %5$s%6$s för att låsa upp alla funktioner i %7$s."

#. translators: %1$s: Yoast SEO
#: src/initializers/crawl-cleanup-permalinks.php:144
msgid "%1$s: redirect utm variables to #"
msgstr "%1$s: omdirigera utm-variabler till #"

#. translators: %1$s: Yoast SEO
#: src/helpers/crawl-cleanup-helper.php:271
msgid "%1$s: unregistered URL parameter removed. See %2$s"
msgstr "%1$s: oregistrerad URL-parameter borttagen. Se %2$s"

#. translators: 1: Link start tag to the Permalinks settings page, 2: Link
#. closing tag.
#: src/integrations/admin/crawl-settings-integration.php:286
msgid "This feature is disabled when your site is not using %1$spretty permalinks%2$s."
msgstr "Denna funktion är inaktiverad om din webbplats inte använder %1$ssnygga permalänkar%2$s."

#: src/integrations/admin/crawl-settings-integration.php:292
msgid "This feature is disabled when your site is not using pretty permalinks."
msgstr "Denna funktion är inaktiverad om din webbplats inte använder snygga permalänkar."

#: src/presenters/admin/indexing-notification-presenter.php:93
msgid "It looks like you've enabled media pages. We recommend that you help us to re-analyze your site by running the SEO data optimization."
msgstr "Det verkar som att du har aktiverat mediasidor. Vi rekommenderar att du hjälper oss att analysera din webbplats igen genom att köra SEO-dataoptimeringen."

#. translators: CTA to finish the first time configuration. %s: Either
#. first-time SEO configuration or SEO configuration.
#: admin/class-admin.php:235
msgid "Finish your %s"
msgstr "Slutför din %s"

#: inc/class-wpseo-rank.php:156 inc/class-wpseo-rank.php:245
#: js/dist/block-editor.js:534 js/dist/editor-modules.js:310
#: js/dist/editor-modules.js:574 js/dist/elementor.js:84
#: js/dist/externals-components.js:183 js/dist/externals-components.js:525
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:1
#: js/dist/term-edit.js:1
msgid "Potentially non-inclusive"
msgstr "Möjligtvis inte inkluderande"

#: inc/class-wpseo-admin-bar-menu.php:547
msgid "Help"
msgstr "Hjälp"

#: inc/class-wpseo-admin-bar-menu.php:528
msgid "Write better content"
msgstr "Skriv bättre innehåll"

#: inc/class-wpseo-admin-bar-menu.php:518
#: inc/class-wpseo-admin-bar-menu.php:577
msgid "Learn more SEO"
msgstr "Lär dig mer SEO"

#: inc/class-wpseo-admin-bar-menu.php:473
msgid "SEO Tools"
msgstr "SEO-verktyg"

#: inc/class-wpseo-admin-bar-menu.php:230
msgid "not set"
msgstr "inte angivet"

#: inc/class-wpseo-admin-bar-menu.php:523
msgid "Improve your blog post"
msgstr "Förbättra dina blogginlägg"

#: inc/class-wpseo-admin-bar-menu.php:572
msgid "WordPress.org support forums"
msgstr "WordPress.org supportforum"

#: inc/class-wpseo-admin-bar-menu.php:567
msgid "Yoast Premium support"
msgstr "Yoast premiumsupport"

#: inc/class-wpseo-admin-bar-menu.php:236
msgid "Focus keyphrase: "
msgstr "Fokusnyckelordsfras: "

#: inc/class-wpseo-admin-bar-menu.php:562
msgid "Yoast.com help section"
msgstr "Hjälpavsnitt för Yoast.com"

#. translators: %1$s is the site url of the site that is cleaned up. %2$s is
#. the name of the cleanup task that is currently running.
#: src/commands/cleanup-command.php:159
msgid "Cleaning up %1$s [%2$s]"
msgstr "Rensar upp %1$s [%2$s]"

#. translators: %1$s is the site url of the site that is skipped. %2$s is Yoast
#. SEO.
#: src/commands/cleanup-command.php:146
msgid "Skipping %1$s. %2$s is not active on this site."
msgstr "Hoppar över %1$s. %2$s är inte aktiv på denna webbplats."

#. translators: %1$d is the number of records that are removed.
#: src/commands/cleanup-command.php:97
msgid "Cleaned up %1$d record."
msgid_plural "Cleaned up %1$d records."
msgstr[0] "Rensade upp %1$d post."
msgstr[1] "Rensade upp %1$d poster."

#. translators: %1$d is the number of records that were removed. %2$s is the
#. site url.
#: src/commands/cleanup-command.php:183
msgid "Cleaned up %1$d record from %2$s."
msgid_plural "Cleaned up %1$d records from %2$s."
msgstr[0] "Rensade %1$d post från %2$s."
msgstr[1] "Rensade %1$d poster från %2$s."

#. translators: %s: expands to the term id
#: src/exceptions/indexable/term-not-built-exception.php:20
msgid "The term %s could not be built because it's not indexable."
msgstr "Termen %s kunde inte byggas eftersom den inte är indexerbar."

#. translators: %s: expands to the post id
#: src/exceptions/indexable/post-not-built-exception.php:32
msgid "The post %s could not be indexed because it's post type is excluded from indexing."
msgstr "Inlägget %s kunde inte indexeras eftersom dess inläggstyp är exkluderad från indexering."

#. translators: %s: expands to the post id
#: src/exceptions/indexable/post-not-built-exception.php:20
msgid "The post %s could not be indexed because it does not meet indexing requirements."
msgstr "Inlägget %s kunde inte indexeras eftersom det inte uppfyller indexeringskraven."

#: src/commands/cleanup-command.php:84
msgid "The value for 'batch-size' must be a positive integer higher than equal to 1."
msgstr "Värdet för ”batch-size” måste vara ett positivt heltal som är större än eller lika med 1."

#: src/commands/cleanup-command.php:81
msgid "The value for 'interval' must be a positive integer."
msgstr "Värdet för ”interval” måste vara ett positivt heltal."

#: src/presenters/admin/indexing-notification-presenter.php:87
msgid "We need to re-analyze some of your SEO data because of a change in the visibility of your post types. Please help us do that by running the SEO data optimization."
msgstr "Vi behöver analysera en del av dina SEO-data på nytt efter att dina inläggstypers synlighet förändrats. Hjälp oss med detta genom att köra en optimering av SEO-data."

#: src/presenters/admin/indexing-notification-presenter.php:90
msgid "We need to re-analyze some of your SEO data because of a change in the visibility of your taxonomies. Please help us do that by running the SEO data optimization."
msgstr "Vi behöver analysera en del av dina SEO-data på nytt efter att dina taxonomiers synlighet förändrats. Hjälp oss med detta genom att köra en optimering av SEO-data."

#: admin/views/class-yoast-feature-toggles.php:210 js/dist/general-page.js:17
#: js/dist/general-page.js:29 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:18
#: js/dist/integrations-page.js:51 js/dist/new-settings.js:324
#: js/dist/plans.js:2 js/dist/post-edit.js:4
msgid "Learn more"
msgstr "Lär dig mer"

#: src/integrations/admin/crawl-settings-integration.php:157
msgid "Redirect pretty URLs for search pages to raw format"
msgstr "Omdirigera snygga URL:er för söksidor till motsvarande obearbetat format"

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO page of the Permalink Cleanup features, %2$s expands to a closing
#. anchor tag.
#: src/integrations/admin/crawl-settings-integration.php:204
msgid "These are expert features, so make sure you know what you're doing before removing the parameters. %1$sRead more about how your site can be affected%2$s."
msgstr "Detta är expertfunktioner, se till att du vet vad du gör innan du tar bort parametrarna. %1$sLäs mer om hur din webbplats kan påverkas%2$s."

#. translators: %1$s expands to Yoast SEO, %2$s expands to the name of the
#. class that could not be found.
#: src/loader.php:258
msgid "%1$s attempted to load the class %2$s but it could not be found."
msgstr "%1$s försökte ladda klassen %2$s men den kunde inte hittas."

#: src/integrations/admin/crawl-settings-integration.php:189
#: js/dist/new-settings.js:178
msgid "Remove unused resources"
msgstr "Ta bort oanvända resurser"

#: src/integrations/admin/crawl-settings-integration.php:162
msgid "Prevent search engines from crawling /wp-json/"
msgstr "Förhindra att sökmotorer genomsöker /wp-json/"

#: src/integrations/admin/crawl-settings-integration.php:156
msgid "Prevent search engines from crawling site search URLs"
msgstr "Förhindra att sökmotorer genomsöker webbplatsens sök-URL:er"

#: admin/metabox/class-metabox-section-inclusive-language.php:30
#: js/dist/externals-components.js:527
msgid "Inclusive language"
msgstr "Inkluderande språk"

#: admin/views/user-profile.php:71
#: src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php:109
msgid "Disable inclusive language analysis"
msgstr "Inaktivera analys av inkluderande språk"

#: admin/views/class-yoast-feature-toggles.php:91 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "Inclusive language analysis"
msgstr "Analys av inkluderande språk"

#: admin/views/class-yoast-feature-toggles.php:95
msgid "Discover why inclusive language is important for SEO."
msgstr "Upptäck varför inkluderande språk är viktigt för sökmotorsoptimering."

#: admin/views/class-yoast-feature-toggles.php:94
msgid "The inclusive language analysis offers suggestions to write more inclusive copy."
msgstr "Analysen för inkluderande språk ger förslag på att skriva mer inkluderande texter."

#: admin/views/user-profile.php:74
#: src/user-meta/framework/custom-meta/inclusive-language-analysis-disable.php:115
msgid "Removes the inclusive language analysis section from the metabox and disables all inclusive language-related suggestions."
msgstr "Tar bort sektionen för analys av inkluderande språk från metarutan och inaktiverar alla ”inkluderande språk”-relaterade förslag."

#: inc/class-wpseo-admin-bar-menu.php:269
msgid "Front-end SEO inspector"
msgstr "SEO-granskare i front-end"

#: admin/class-yoast-form.php:933
msgid "Unlock with Premium!"
msgstr "Lås upp med Premium!"

#. translators: 1: Yoast SEO Premium
#: src/integrations/admin/deactivated-premium-integration.php:99
msgid "Activate %1$s!"
msgstr "Aktivera %1$s!"

#: admin/views/class-yoast-feature-toggles.php:195 js/dist/new-settings.js:33
#: js/dist/new-settings.js:328
msgid "IndexNow"
msgstr "IndexNow"

#. translators: 1: Yoast SEO Premium 2: Link start tag to activate premium, 3:
#. Link closing tag.
#: src/integrations/admin/deactivated-premium-integration.php:86
msgid "You've installed %1$s but it's not activated yet. %2$sActivate %1$s now!%3$s"
msgstr "Du har installerat %1$s men det är inte aktiverat ännu. %2$sAktivera %1$s nu!%3$s"

#: src/integrations/admin/crawl-settings-integration.php:149
msgid "Unregistered URL parameters"
msgstr "Oregistrerade URL-parametrar"

#: src/integrations/admin/crawl-settings-integration.php:153
#: js/dist/new-settings.js:33 js/dist/new-settings.js:179
msgid "Filter search terms"
msgstr "Filtrera söktermer"

#: admin/views/class-yoast-feature-toggles.php:199
msgid "Find out how IndexNow can help your site."
msgstr "Ta reda på hur IndexNow kan hjälpa din webbplats."

#: src/deprecated/src/config/wordproof-translations.php:129
msgid "Contact WordProof support"
msgstr "Kontakta WordProof support"

#: src/integrations/admin/crawl-settings-integration.php:212
msgid "Permalink cleanup settings"
msgstr "Inställningar för rensning av permalänkar"

#: src/integrations/admin/crawl-settings-integration.php:155
#: js/dist/new-settings.js:33 js/dist/new-settings.js:179
msgid "Filter searches with common spam patterns"
msgstr "Filtrera sökningar med vanliga skräppostmönster"

#: src/integrations/admin/crawl-settings-integration.php:198
msgid "Search cleanup settings"
msgstr "Inställningar för rensning av sökningar"

#: src/integrations/admin/crawl-settings-integration.php:154
#: js/dist/new-settings.js:33 js/dist/new-settings.js:179
msgid "Filter searches with emojis and other special characters"
msgstr "Filtrera sökningar med emojis och andra specialtecken"

#: src/integrations/admin/crawl-settings-integration.php:148
msgid "Campaign tracking URL parameters"
msgstr "URL-parametrar för kampanjspårning"

#: admin/views/class-yoast-feature-toggles.php:198 js/dist/new-settings.js:328
msgid "Automatically ping search engines like Bing and Yandex whenever you publish, update or delete a post."
msgstr "Pinga automatiskt sökmotorer som Bing och Yandex när du publicerar, uppdaterar eller tar bort ett inlägg."

#: src/helpers/first-time-configuration-notice-helper.php:64
msgid "SEO configuration"
msgstr "SEO-konfiguration"

#: src/integrations/admin/crawl-settings-integration.php:186
msgid "Basic crawl settings"
msgstr "Grundläggande genomsökningsinställningar"

#: src/integrations/admin/crawl-settings-integration.php:144
msgid "Powered by HTTP header"
msgstr "HTTP-header för ”drivs av”"

#: src/integrations/admin/crawl-settings-integration.php:143
#: js/dist/new-settings.js:33 js/dist/new-settings.js:178
msgid "Pingback HTTP header"
msgstr "HTTP-header för pingbacks"

#: src/integrations/admin/crawl-settings-integration.php:161
msgid "Emoji scripts"
msgstr "Emoji-skript"

#: src/integrations/admin/crawl-settings-integration.php:141
msgid "oEmbed links"
msgstr "oEmbed-länkar"

#: src/integrations/admin/crawl-settings-integration.php:140
msgid "RSD / WLW links"
msgstr "RSD-/WLW-länkar"

#: src/integrations/admin/crawl-settings-integration.php:139
msgid "REST API links"
msgstr "REST API-länkar"

#: src/integrations/admin/crawl-settings-integration.php:138
msgid "Shortlinks"
msgstr "Kortlänkar"

#: src/integrations/admin/crawl-settings-integration.php:134
msgid "Atom/RDF feeds"
msgstr "Atom-/RDF-flöden"

#: src/integrations/admin/crawl-settings-integration.php:133
msgid "Search results feeds"
msgstr "Webbflöden för sökresultat"

#: src/integrations/admin/crawl-settings-integration.php:132
msgid "Custom taxonomy feeds"
msgstr "Webbflöden för anpassade taxonomier"

#: src/integrations/admin/crawl-settings-integration.php:131
msgid "Tag feeds"
msgstr "Webbflöden för etiketter"

#: src/integrations/admin/crawl-settings-integration.php:130
msgid "Category feeds"
msgstr "Webbflöden för kategorier"

#: src/integrations/admin/crawl-settings-integration.php:129
msgid "Post type feeds"
msgstr "Webbflöden för inläggstyper"

#: src/integrations/admin/crawl-settings-integration.php:128
msgid "Post authors feeds"
msgstr "Webbflöden för inläggsförfattare"

#: src/integrations/admin/crawl-settings-integration.php:126
msgid "Global comment feeds"
msgstr "Globala webbflöden för kommentarer"

#: src/integrations/admin/crawl-settings-integration.php:125
msgid "Global feed"
msgstr "Globalt webbflöde"

#: src/integrations/admin/crawl-settings-integration.php:142
msgid "Generator tag"
msgstr "Generator-tagg"

#: src/integrations/admin/crawl-settings-integration.php:188
msgid "Feed crawl settings"
msgstr "Genomsökningsinställningar för webbflöde"

#. translators: 1: Link start tag to the first-time configuration, 2: Link
#. closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:119
msgid "We noticed that you haven't fully configured Yoast SEO yet. Optimize your SEO settings even further by using our improved %1$s First-time configuration%2$s."
msgstr "Du har tydligen inte konfigurerat allt i Yoast SEO ännu. Du kan optimera dina SEO-inställningar ytterligare med hjälp av vår rutin för %1$sinitial konfiguration av tillägget%2$s."

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/network/crawl-settings.php:31
msgid "%1$sLearn more about crawl settings.%2$s"
msgstr "%1$sLäs mer om inställningar för genomsökning.%2$s"

#: src/integrations/admin/crawl-settings-integration.php:127
msgid "Post comments feeds"
msgstr "Webbflöden för inläggskommentarer"

#: admin/pages/network.php:25 admin/views/tabs/network/crawl-settings.php:19
msgid "Crawl settings"
msgstr "Genomsökningsinställningar"

#: src/integrations/admin/first-time-configuration-integration.php:128
#: js/dist/general-page.js:49
msgid "First-time configuration"
msgstr "Startkonfiguration"

#. translators: 1: Link start tag to the First time configuration tab in the
#. General page, 2: Link closing tag.
#: admin/views/tabs/tool/import-seo.php:106
msgid "You should finish the %1$sfirst time configuration%2$s to make sure your SEO data has been optimized and you’ve set the essential Yoast SEO settings for your site."
msgstr "Du bör genomföra %1$sden inledande konfigurationen%2$s så att dina SEO-data är optimerade och att du har gjort de grundläggande inställningarna för Yoast SEO på din webbplats."

#: admin/views/tabs/tool/import-seo.php:100
msgid "Step 4: Go through the first time configuration"
msgstr "Steg 4: Gå igenom förstagångskonfigurationen"

#: src/exceptions/importing/aioseo-validation-exception.php:17
msgid "The validation of the AIOSEO data structure has failed."
msgstr "Valideringen av strukturen hos datan för AIOSEO misslyckades."

#: src/integrations/admin/import-integration.php:214
msgid "The AIOSEO import was cancelled because some AIOSEO data is missing. Please try and take the following steps to fix this:"
msgstr "Importen från AIOSEO avbröts eftersom vissa AIOSEO-data saknas. Försök att vidta följande åtgärder för att åtgärda detta:"

#: src/integrations/admin/import-integration.php:217
msgid "If you have never saved any AIOSEO 'Search Appearance' settings, please do that first and run the import again."
msgstr "Om du aldrig har sparat inställningarna i AIOSEO för ”utseende i sökresultat” bör du börja med att göra det och sedan köra importen en gång till."

#: src/integrations/admin/import-integration.php:220
msgid "If you already have saved AIOSEO 'Search Appearance' settings and the issue persists, please contact our support team so we can take a closer look."
msgstr "Om du redan har sparat inställningarna i AIOSEO för ”utseende i sökresultat” och problemet kvarstår ber vi dig kontakta vårt supportteam så att vi kan titta närmare på det."

#. translators: %s expands to WordProof
#: src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php:116
msgid "Currently, the %s integration is not available for multisites."
msgstr "För närvarande är inte %s-integrationen tillgänglig för multisites."

#: src/deprecated/src/config/wordproof-translations.php:115
msgid "Open settings"
msgstr "Öppna inställningar"

#: src/deprecated/src/config/wordproof-translations.php:101
msgid "Open authentication"
msgstr "Öppna autentisering"

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:42
msgid "%s has successfully timestamped this page."
msgstr "%s har tidsstämplat denna sida."

#: src/deprecated/src/integrations/third-party/wordproof-integration-toggle.php:122
msgid "The WordProof Timestamp plugin needs to be disabled before you can activate this integration."
msgstr "”WordProof Timestamp”-tillägget måste inaktiveras innan du kan aktivera denna integration."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:87
msgid "The timestamp is not created because you need to authenticate with %s first."
msgstr "Tidsstämpeln skapas inte eftersom du måste autentisera med %s först."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:27
msgid "You are out of timestamps. Please upgrade your account by opening the %s settings."
msgstr "Du har slut på tidsstämplar. Uppgradera ditt konto genom att öppna %s-inställningarna."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:72
msgid "The timestamp is not retrieved by your site. Please try again or contact %1$s support."
msgstr "Tidsstämpeln hämtas inte av din webbplats. Försök igen eller kontakta %1$s-supporten."

#. translators: %s expands to WordProof.
#: src/deprecated/src/config/wordproof-translations.php:57
msgid "%1$s failed to timestamp this page. Please check if you're correctly authenticated with %1$s and try to save this page again."
msgstr "%1$s misslyckades tidsstämpla denna sida. Kontrollera om du är korrekt autentiserad med %1$s och försök spara den här sidan igen."

#: src/integrations/admin/import-integration.php:236
msgid "Cleanup failed with the following error:"
msgstr "Rensningen misslyckades med följande fel:"

#. translators: %s: expands to the name of the plugin that is selected to be
#. imported
#: src/integrations/admin/import-integration.php:111
msgid "The import from %s includes:"
msgstr "Importen från %s inkluderar:"

#: admin/views/tool-import-export.php:35
#: src/integrations/admin/import-integration.php:107
msgid "Clean up"
msgstr "Rensa"

#: src/integrations/admin/import-integration.php:97
msgid "The cleanup can take a long time depending on your site's size."
msgstr "Rensningen kan ta lång tid beroende på din webbplats storlek."

#: src/integrations/admin/import-integration.php:115
#: src/integrations/admin/import-integration.php:125
msgid "Post metadata (SEO titles, descriptions, etc.)"
msgstr "Inläggsmetadata (SEO-rubriker, beskrivningar, osv.)"

#: src/integrations/admin/import-integration.php:120
msgid "Note: These settings will overwrite the default settings of Yoast SEO."
msgstr "Obs: Dessa inställningar kommer skriva över standardinställningarna för Yoast SEO."

#: src/integrations/admin/import-integration.php:108
msgid "Please select an SEO plugin below to see what data can be imported."
msgstr "Välj ett SEO-tillägg nedan för att se vilka data som kan importeras."

#: src/integrations/admin/import-integration.php:99
msgid "After you've imported data from another SEO plugin, please make sure to clean up all the original data from that plugin. (step 5)"
msgstr "Efter du har importerat data från ett annat SEO-tillägg, se till att rensa upp all originaldata från det tillägget. (steg 5)"

#: src/integrations/admin/import-integration.php:109
msgid "Once you're certain that your site is working properly with the imported data from another SEO plugin, you can clean up all the original data from that plugin."
msgstr "När du är säker att din webbplats fungerar korrekt med den importerade datan från andra SEO-tillägg, kan du rensa upp all originaldata från det tillägget."

#: src/integrations/admin/import-integration.php:116
#: src/integrations/admin/import-integration.php:126
msgid "Note: This metadata will only be imported if there is no existing Yoast SEO metadata yet."
msgstr "Obs: Denna metadata kommer endast att importeras om det inte finns någon befintlig Yoast SEO-metadata ännu."

#: src/integrations/admin/import-integration.php:98
msgid "Note: "
msgstr "Observera: "

#: src/integrations/admin/import-integration.php:100
msgid "Select SEO plugin"
msgstr "Välj SEO-tillägg"

#: src/integrations/admin/import-integration.php:238
msgid "Import failed with the following error:"
msgstr "Import misslyckades med följande fel:"

#: src/integrations/admin/import-integration.php:101
msgid "No data found from other SEO plugins."
msgstr "Ingen data hittades från andra SEO-tillägg."

#: src/integrations/admin/import-integration.php:96
msgid "The import can take a long time depending on your site's size."
msgstr "Importen kan ta lång tid beroende på din webbplats storlek."

#: src/integrations/admin/installation-success-integration.php:104
msgid "Installation Successful"
msgstr "Installationen lyckades"

#: src/config/schema-types.php:131
msgid "Blog Post"
msgstr "Blogginlägg"

#. translators: %s: expands to 'Yoast SEO Premium'.
#. translators: 1: Yoast WooCommerce SEO
#: src/integrations/admin/workouts-integration.php:315
#: js/dist/integrations-page.js:11
msgid "Activate %s"
msgstr "Aktivera %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:309
msgid "Update %s"
msgstr "Uppdatera %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:302
msgid "Renew %s"
msgstr "Förnya %s"

#: src/integrations/admin/workouts-integration.php:243
msgid "Get help activating your subscription"
msgstr "Skaffa hjälp med att aktivera din prenumeration"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:237
msgid "It looks like you’re running an outdated and unactivated version of %1$s, please activate your subscription in %2$sMyYoast%3$s and update to the latest version (at least 17.7) to gain access to our updated workouts section."
msgstr "Det verkar som om du använder en föråldrad version av %1$s som inte är aktiverad. Aktivera ditt abonnemang på %2$sMyYoast%3$s och uppdatera till den senaste versionen (lägst 17.7) för att få tillgång till vårt uppdaterade avsnitt med uppdrag."

#: src/integrations/admin/workouts-integration.php:213
msgid "Renew your subscription"
msgstr "Förnya din prenumeration"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:233
msgid "Activate your subscription of %s"
msgstr "Aktivera din prenumeration på %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:221
msgid "Update to the latest version of %s"
msgstr "Uppdatera till den senaste versionen av %s"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:224
msgid "It looks like you're running an outdated version of %1$s, please %2$supdate to the latest version (at least 17.7)%3$s to gain access to our updated workouts section."
msgstr "Det verkar som att du kör en föråldrad version av %1$s, %2$suppdatera till den senaste versionen (minst 17.7)%3$s för att få tillgång till vår uppdaterade träningssektion."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:206
msgid "Accessing the latest workouts requires an updated version of %s (at least 17.7), but it looks like your subscription has expired. Please renew your subscription to update and gain access to all the latest features."
msgstr "För att nå de sista uppdragen i listan behöver du en uppdaterad version av %s (lägst version 17.7). Men ditt abonnemang verkar inte längre vara giltigt. Förnya abonnemanget för att få våra uppdateringar och kunna använda alla de senaste funktionerna."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:203
msgid "Renew your subscription of %s"
msgstr "Förnya din prenumeration på %s"

#. translators: 1: Link start tag to the first-time configuration, 2: Yoast
#. SEO, 3: Link closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:110
msgid "Get started quickly with the %1$s%2$s First-time configuration%3$s and configure Yoast SEO with the optimal SEO settings for your site!"
msgstr "Kom igång snabbt med %1$sden initiala konfigurationen av %2$s%3$s och ställ in Yoast SEO för optimal hantering av SEO på din webbplats!"

#: inc/class-wpseo-replace-vars.php:1499
msgid "Current or first category title"
msgstr "Nuvarande eller första kategorirubrik"

#: inc/class-wpseo-replace-vars.php:1499
msgid "Category Title"
msgstr "Kategorirubrik"

#: inc/class-wpseo-replace-vars.php:1498
msgid "Replaced with the post content"
msgstr "Ersatt med inläggets innehåll"

#: inc/class-wpseo-replace-vars.php:1498
msgid "Post Content"
msgstr "Inläggsinnehåll"

#: inc/class-wpseo-replace-vars.php:1497
msgid "Replaced with the permalink"
msgstr "Ersatt med permalänken"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Replaced with the last name of the author"
msgstr "Ersatt med författarens efternamn"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Author last name"
msgstr "Författarens efternamn"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Replaced with the first name of the author"
msgstr "Ersatt med författarens förnamn"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Author first name"
msgstr "Författarens förnamn"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Post day"
msgstr "Inläggsdag"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Replaced with the month the post was published"
msgstr "Ersatt med månaden som inlägget publicerades"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Post month"
msgstr "Inläggsmånad"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Replaced with the year the post was published"
msgstr "Ersatt med året som inlägget publicerades"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Post year"
msgstr "Inläggsår"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Current day"
msgstr "Nuvarande dag"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Current month"
msgstr "Nuvarande månad"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Current date"
msgstr "Nuvarande datum"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Replaced with the day the post was published"
msgstr "Ersatt med dagen som inlägget publicerades"

#. translators: %1$s expands to an opening strong tag, %2$s expands to the
#. dependency name, %3$s expands to a closing strong tag, %4$s expands to an
#. opening anchor tag, %5$s expands to a closing anchor tag.
#: admin/class-suggested-plugins.php:111
msgid "It looks like you aren't using our %1$s%2$s addon%3$s. %4$sUpgrade today%5$s to unlock more tools and SEO features to make your products stand out in search results."
msgstr "Det verkar som om du inte använder vår %1$sutökning %2$s%3$s. %4$sUppgradera nu%5$s för att få tillgång till fler verktyg och SEO-funktioner som kan lyfta fram dina produkter bland sökresultaten."

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:102
msgid "Below are the technical details for the error. See %1$sthis page%2$s for a more detailed explanation."
msgstr "Nedan finns de tekniska detaljerna för felet. Se %1$sdenna sida%2$s för en mer detaljerad förklaring."

#: src/integrations/admin/workouts-integration.php:93
msgid "Workouts"
msgstr "Träningspass"

#: admin/views/class-yoast-integration-toggles.php:81
msgid "Improve the quality of your site search! Automatically helps your users find your cornerstone and most important content in your internal search results. It also removes noindexed posts & pages from your site’s search results."
msgstr "Förbättra kvaliteten på din webbplatssökning! Hjälper automatiskt dina användare att hitta ditt hörnstensinnehåll och det viktigaste innehållet i dina interna sökresultat. Det eliminerar även sökresultat som leder till inlägg och sidor som är markerade med ”noindex”."

#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:83
msgid "Find out more about our %s integration."
msgstr "Ta reda på mer om vår %s-integration."

#: admin/views/class-yoast-feature-toggles.php:129
msgid "Read more about how internal linking can improve your site structure."
msgstr "Läs mer om hur intern länkning kan förbättra din webbplatsstruktur."

#: admin/views/class-yoast-feature-toggles.php:128
msgid "Get relevant internal linking suggestions — while you’re writing! The link suggestions metabox shows a list of posts on your blog with similar content that might be interesting to link to. "
msgstr "Få relevanta interna länkförslag – medan du skriver! Metarutan länkförslag visar en lista med inlägg på din blogg som har liknande innehåll och det kan vara intressant att länka till. "

#: admin/views/class-yoast-feature-toggles.php:125 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "Link suggestions"
msgstr "Länkförslag"

#: admin/views/class-yoast-feature-toggles.php:119
msgid "Find out how Insights can help you improve your content."
msgstr "Ta reda på hur insikter kan hjälpa dig att förbättra ditt innehåll."

#: admin/views/class-yoast-feature-toggles.php:118
msgid "Find relevant data about your content right in the Insights section in the Yoast SEO metabox. You’ll see what words you use most often and if they’re a match with your keywords! "
msgstr "Hitta relevant data om ditt innehåll direkt i sektionen för insikter i Yoast SEO-metarutan. Du ser vilka ord du använder oftast och om de matchar med dina nyckelord! "

#: admin/views/class-yoast-feature-toggles.php:116 js/dist/block-editor.js:238
#: js/dist/block-editor.js:542 js/dist/classic-editor.js:223
#: js/dist/elementor.js:110 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "Insights"
msgstr "Insikter"

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:76
#: src/presenters/admin/indexing-failed-notification-presenter.php:77
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please make sure to activate your subscription in MyYoast by completing %1$sthese steps%2$s."
msgstr "Hoppsan, något har gått fel och vi kunde inte slutföra optimeringen av dina SEO-data. Se till att aktivera din prenumeration i MyYoast genom att slutföra %1$sdessa steg%2$s."

#. Translators: %s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:192
msgid "Addon installation failed because of an error: %s."
msgstr "Installation av utökning misslyckades på grund av ett fel: %s."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:184
msgid "Addon installed."
msgstr "Utökning installerad."

#. Translators:%s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:160
msgid "Addon activation failed because of an error: %s."
msgstr "Aktivering av utökning misslyckades på grund av ett fel: %s."

#: src/integrations/admin/addon-installation/installation-integration.php:156
msgid "You are not allowed to activate plugins."
msgstr "Du har inte behörighet att aktivera tillägg."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:154
msgid "Addon activated."
msgstr "Utökning aktiverad."

#. translators: %1$s expands to an anchor tag to the admin premium page, %2$s
#. expands to Yoast SEO Premium, %3$s expands to a closing anchor tag
#: src/integrations/admin/addon-installation/installation-integration.php:129
msgid "%1$s Continue to %2$s%3$s"
msgstr "%1$s Fortsätt till %2$s%3$s"

#: src/integrations/admin/addon-installation/installation-integration.php:106
msgid "Installing and activating addons"
msgstr "Installerar och aktiverar utökningar"

#. translators: %1$s expands to Yoast SEO
#: src/integrations/admin/addon-installation/dialog-integration.php:94
msgid "No %1$s plugins have been installed. You don't seem to own any active subscriptions."
msgstr "Inga %1$s-tillägg har installerats. Du verkar inte äga några aktiva prenumerationer."

#: src/integrations/admin/addon-installation/installation-integration.php:188
msgid "You are not allowed to install plugins."
msgstr "Du har inte behörighet att installera tillägg."

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-admin.php:248
msgid "Required by %s"
msgstr "Krävs av %s"

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:95
msgid "Auto-updates are disabled based on this setting for %1$s."
msgstr "Automatiska uppdateringar är inaktiverade på grund av denna inställning för %1$s."

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:85
msgid "Auto-updates are enabled based on this setting for %1$s."
msgstr "Automatiska uppdateringar är aktiverade baserat på denna inställning för %1$s."

#: src/presenters/admin/badge-presenter.php:80
#: src/presenters/admin/badge-presenter.php:87
#: js/dist/externals/componentsNew.js:1081 js/dist/integrations-page.js:3
#: js/dist/integrations-page.js:6 js/dist/integrations-page.js:18
#: js/dist/new-settings.js:366
msgid "New"
msgstr "Nytt"

#: src/exceptions/indexable/post-not-found-exception.php:16
msgid "The post could not be found."
msgstr "Inlägget kunde inte hittas."

#: src/exceptions/indexable/term-not-found-exception.php:16
msgid "The term could not be found."
msgstr "Termen kunde inte hittas."

#. translators: %s is the reason given by WordPress.
#: src/exceptions/indexable/invalid-term-exception.php:21
msgid "The term is considered invalid. The following reason was given by WordPress: %s"
msgstr "Termen anses ogiltig. Följande anledning gavs av WordPress: %s"

#: admin/class-yoast-form.php:1068 js/dist/general-page.js:46
msgid "This feature has been disabled since subsites never send tracking data."
msgstr "Denna funktion har inaktiverats eftersom underwebbplatser aldrig skickar spårningsdata."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to an
#. closing anchor tag.
#: src/integrations/third-party/wpml-wpseo-notification.php:110
msgid "We notice that you have installed WPML. To make sure your canonical URLs are set correctly, %1$sinstall and activate the WPML SEO add-on%2$s as well!"
msgstr "Vi märker att du har installerat WPML. För att se till att dina kanoniska URL:er är korrekt inställda, %1$sinstallera och aktivera utökningen WPML SEO%2$s också!"

#: src/presenters/admin/indexing-notification-presenter.php:81
msgid "Because of a change in your category base setting, some of your SEO data needs to be reprocessed."
msgstr "Eftersom dina basinställningar ändrats för kategorier behöver vissa av dina SEO-uppgifter bearbetas på nytt."

#: admin/views/class-yoast-feature-toggles.php:189 js/dist/new-settings.js:324
msgid "This adds an author byline and reading time estimate to the article’s snippet when shared on Slack."
msgstr "Detta lägger till en ”byline” för författaren och beräknad lästid i artikelns förhandsvisningstext vid delning på Slack."

#: admin/views/class-yoast-feature-toggles.php:190
msgid "Find out how a rich snippet can improve visibility and click-through-rate."
msgstr "Ta reda på hur en rik förhandsvisningstext kan förbättra synlighet och klickfrekvens."

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s minut"
msgstr[1] "%s minuter"

#: src/presenters/slack/enhanced-data-presenter.php:50
msgid "Written by"
msgstr "Skriven av"

#: admin/views/class-yoast-feature-toggles.php:187
msgid "Enhanced Slack sharing"
msgstr "Förbättrad Slack-delning"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "Est. reading time"
msgstr "Beräknad lästid"

#. translators: 1: Expands to Yoast SEO
#: src/presenters/admin/indexing-notification-presenter.php:129
msgid "Wait for a week or so, until %1$s automatically processes most of your content in the background."
msgstr "Vänta i cirka en vecka tills %1$s automatiskt kommer att ha bearbetat det mesta av ditt innehåll i bakgrunden."

#: inc/class-wpseo-admin-bar-menu.php:444
msgid "Google Rich Results Test"
msgstr "Test av Google Rich Results"

#: src/presenters/admin/indexing-notification-presenter.php:84
msgid "Because of a change in your tag base setting, some of your SEO data needs to be reprocessed."
msgstr "Eftersom dina URL-inställningar ändrats för etiketter behöver vissa av dina SEO-uppgifter bearbetas på nytt."

#. translators: %s: 'Semrush'
#: admin/views/class-yoast-integration-toggles.php:71
msgid "The %s integration offers suggestions and insights for keywords related to the entered focus keyphrase."
msgstr "Integrationen med %s erbjuder förslag och insikter för nyckelord som är relaterade till den angivna fokusnyckelordsfrasen."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/integrations.php:27
msgid "This tab allows you to selectively disable %1$s integrations with third-party products for all sites in the network. By default all integrations are enabled, which allows site admins to choose for themselves if they want to toggle an integration on or off for their site. When you disable an integration here, site admins will not be able to use that integration at all."
msgstr "Denna flik låter dig selektivt inaktivera integrationen hos %1$s med tredjepartsprodukter för alla webbplatser i nätverket. Som standard är alla integrationer aktiva så att administratörerna för varje webbplats själva kan välja om de vill slå på eller av någon integration för sin webbplats. Om du inaktiverar någon integration här kommer administratörerna för webbplatser inte att kunna använda den integrationen över huvud taget."

#: admin/class-admin.php:259
msgid "Activate your subscription"
msgstr "Aktivera din prenumeration"

#: src/presenters/admin/indexing-error-presenter.php:64
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please click the button again to re-start the process. "
msgstr "Hoppsan, något har gått fel och vi kunde inte slutföra optimeringen av dina SEO-data. Klicka på knappen igen för att starta om processen. "

#: src/presenters/admin/indexing-list-item-presenter.php:42
msgid "Learn more about the benefits of optimized SEO data."
msgstr "Läs mer om fördelarna med optimerad SEO-data."

#: src/presenters/admin/indexing-notification-presenter.php:59
#: js/dist/general-page.js:42 js/dist/indexation.js:8
msgid "Start SEO data optimization"
msgstr "Starta SEO-dataoptimering"

#: src/integrations/watchers/indexable-homeurl-watcher.php:97
msgid "All permalinks were successfully reset"
msgstr "Alla permalänkar återställdes utan problem"

#: src/presenters/admin/indexing-list-item-presenter.php:40
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. If you have a lot of content it might take a while, but trust us, it's worth it."
msgstr "Du kan snabba upp din webbplats och få insikter om strukturen hos dina interna länkar om du låter oss göra några optimeringar i hur dina SEO-data sparas. Om du har mycket innehåll kan det ta ett tag, men vi lovar att det är mödan värt."

#: src/presenters/admin/indexing-notification-presenter.php:96
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored."
msgstr "Du kan snabba upp din webbplats och få insikt i din interna länkstruktur genom att låta oss utföra några optimeringar för hur SEO-data lagras."

#: src/presenters/admin/indexing-list-item-presenter.php:37
msgid "Optimize SEO Data"
msgstr "Optimera SEO-data"

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO tools page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-failed-notification-presenter.php:59
msgid "Something has gone wrong and we couldn't complete the optimization of your SEO data. Please %1$sre-start the process%2$s."
msgstr "Något har gått fel och vi kunde inte slutföra optimeringen av dina SEO-data. %1$sÅterstarta processen%2$s."

#: src/presenters/admin/indexing-error-presenter.php:71
#: src/presenters/admin/indexing-failed-notification-presenter.php:71
msgid "If the problem persists, please contact support."
msgstr "Om detta problem kvarstår, kontakta supporten."

#: inc/class-addon-manager.php:871
msgid "Need support?"
msgstr "Behöver du hjälp?"

#. translators: 1: expands to <a> that refers to the help page, 2: </a> closing
#. tag.
#: inc/class-addon-manager.php:874
msgid "You can probably find an answer to your question in our %1$shelp center%2$s."
msgstr "Du kan troligen hitta ett svar till din fråga i vårt %1$shjälpcenter%2$s."

#. translators: %s expands to a mailto support link.
#: inc/class-addon-manager.php:877
msgid "If you still need support and have an active subscription for this product, please email %s."
msgstr "Om du fortfarande behöver support och har en aktiv prenumeration på den här produkten, skicka e-post till %s."

#. translators: %1$s: expands to an opening anchor tag, %2$s: expands to a
#. closing anchor tag
#: admin/views/class-yoast-feature-toggles.php:241
msgid "Disabling Yoast SEO's XML sitemaps will not disable WordPress' core sitemaps. In some cases, this %1$s may result in SEO errors on your site%2$s. These may be reported in Google Search Console and other tools."
msgstr "Inaktivera XML-webbplatskartor från Yoast SEO inaktiverar inte WordPress-kärnans webbplatskartor. I vissa fall kan%1$sdetta resultera i SEO-fel på din webbplats%2$s Dessa kan rapporteras i Google Search Console och andra verktyg."

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:158
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. The settings in the schema tab allows a user to change schema meta data for a post. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "Den avancerade sektionen av %1$s-metarutan låter en användare ta bort inlägg från sökresultat eller ändra den kanoniska adressen. Inställningarna under schemafliken låter användaren ändra uppgifter i meta-datats schema för ett inlägg. Detta är saker som du kanske inte vill att någon författare ska göra. Därför kan endast redaktörer och administratörer göra detta som standard. Om du ställer in detta till ”%2$s” kommer alla användare kunna ändra dessa inställningar."

#: admin/views/class-yoast-feature-toggles.php:154
msgid "Security: no advanced or schema settings for authors"
msgstr "Säkerhet: inga avancerade eller schema-inställningar för författare"

#: src/config/schema-types.php:155
msgid "Tech Article"
msgstr "Teknisk artikel"

#: src/config/schema-types.php:151
msgid "Scholarly Article"
msgstr "Vetenskaplig artikel"

#: src/config/schema-types.php:147
msgid "Satirical Article"
msgstr "Satirisk artikel"

#: src/config/schema-types.php:139
msgid "News Article"
msgstr "Nyhetsartikel"

#: src/config/schema-types.php:127
msgid "Article"
msgstr "Artikel"

#: src/config/schema-types.php:88
msgid "Medical Web Page"
msgstr "Medicinsk webbsida"

#: src/config/schema-types.php:84
msgid "Contact Page"
msgstr "Kontaktsida"

#: src/config/schema-types.php:80
msgid "Profile Page"
msgstr "Profilsida"

#: src/config/schema-types.php:60
msgid "Web Page"
msgstr "Webbsida"

#: src/config/schema-types.php:104
msgid "Search Results Page"
msgstr "Sida för sökresultat"

#: src/config/schema-types.php:96
msgid "Checkout Page"
msgstr "Sida för kassa"

#: src/config/schema-types.php:76
msgid "QA Page"
msgstr "Sida för frågor och svar"

#: src/config/schema-types.php:72
msgid "FAQ Page"
msgstr "Sida för vanliga frågor"

#: src/config/schema-types.php:143
msgid "Advertiser Content Article"
msgstr "Artikel med annons-innehåll"

#: src/config/schema-types.php:159
msgid "Report"
msgstr "Rapport"

#: src/config/schema-types.php:68
msgid "About Page"
msgstr "”Om”-sida"

#: src/config/schema-types.php:64 js/dist/block-editor.js:521
#: js/dist/classic-editor.js:506 js/dist/elementor.js:379
msgid "Item Page"
msgstr "Varusida"

#: src/config/schema-types.php:92
msgid "Collection Page"
msgstr "Samlingssida"

#: src/config/schema-types.php:135
msgid "Social Media Posting"
msgstr "Sociala mediepubliceringar"

#: src/config/schema-types.php:100
msgid "Real Estate Listing"
msgstr "Fastighetslista"

#: admin/views/class-yoast-feature-toggles.php:165
#: admin/views/class-yoast-feature-toggles.php:166 js/dist/new-settings.js:33
#: js/dist/new-settings.js:321
msgid "Usage tracking"
msgstr "Användningsspårning"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:170
msgid "Allow us to track some data about your site to improve our plugin."
msgstr "Tillåt att vi spårar viss data om din webbplats för att förbättra vårt tillägg."

#: src/presenters/admin/indexing-notification-presenter.php:75
msgid "Because of a change in your permalink structure, some of your SEO data needs to be reprocessed."
msgstr "På grund av en ändring i din permalänkstruktur måste vissa av dina SEO-data bearbetas igen."

#: src/presenters/admin/indexing-notification-presenter.php:78
msgid "Because of a change in your home URL setting, some of your SEO data needs to be reprocessed."
msgstr "På grund av en förändring i dina inställningar för startsidans URL behöver en del av dina SEO-uppgifter bearbetas på nytt."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:45
msgid "%1$s Internal Linking Blocks"
msgstr "%1$s interna länkningsblock"

#: src/services/health-check/links-table-reports.php:45
msgid "The text link counter is working as expected"
msgstr "Textlänksräknaren fungerar som den ska"

#. translators: 1: Link to the Yoast SEO blog, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:73
msgid "The text link counter helps you improve your site structure. %1$sFind out how the text link counter can enhance your SEO%2$s."
msgstr "Textlänksräknaren hjälper dig att förbättra din webbplatsstruktur. %1$sTa reda på hur textlänksräknaren kan förbättra din SEO%2$s."

#: src/services/health-check/links-table-reports.php:58
msgid "The text link counter feature is not working as expected"
msgstr "Funktionen för räkning av textlänkar fungerar inte som förväntat"

#. translators: %1$s: Link to article about text links, %2$s: Anchor closing
#. tag, %3$s: Emphasis open tag, %4$s: Emphasis close tag
#: admin/class-yoast-columns.php:52
msgid "The links columns show the number of articles on this site linking %3$sto%4$s this article and the number of URLs linked %3$sfrom%4$s this article. Learn more about %1$show to use these features to improve your internal linking%2$s, which greatly enhances your SEO."
msgstr "Länkkolumnerna visar antalet artiklar på den här webbplatsen som länkar %3$still%4$s den här artikeln och antalet URL:er länkade %3$sfrån%4$s den här artikeln. Läs mer om %1$shur du använder dessa funktioner för att förbättra din interna länkning%2$s, vilket i hög grad förbättrar din SEO."

#. translators: 1: Link to the Yoast help center, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:100
msgid "%1$sFind out how to solve this problem on our help center%2$s."
msgstr "%1$sTa reda på hur du löser detta problem i vårt hjälpcenter%2$s."

#: src/presenters/admin/migration-error-presenter.php:64
msgid "Show debug information"
msgstr "Visa felsökningsinformation"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:58
msgid "Your site will continue to work normally, but won't take full advantage of %s."
msgstr "Din webbplats fortsätter att fungera normalt, men kommer inte att dra full nytta av %s."

#: src/presenters/admin/search-engines-discouraged-presenter.php:41
msgid "I don't want this site to show in the search results."
msgstr "Jag vill inte att den här webbplatsen ska visas i sökresultaten."

#. translators: %1$s: Yoast SEO
#: admin/class-yoast-columns.php:36
msgid "%1$s adds several columns to this page."
msgstr "%1$s lägger till flera kolumner på denna sida."

#. translators: %1$s: Link to article about content analysis, %2$s: Anchor
#. closing
#: admin/class-yoast-columns.php:43
msgid "We've written an article about %1$show to use the SEO score and Readability score%2$s."
msgstr "Vi har skrivet en artikel om %1$shur du använder SEO-poäng och läsbarhetspoäng%2$s."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:47
msgid "%s had problems creating the database tables needed to speed up your site."
msgstr "%s hade problem med att skapa databastabellerna som behövs för att snabba upp din webbplats."

#. translators: 1: Link start tag to the WordPress Reading Settings page, 2:
#. Link closing tag.
#: src/presenters/admin/search-engines-discouraged-presenter.php:36
msgid "If you want search engines to show this site in their results, you must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Om du vill att sökmotorer ska visa den här webbplatsen i sina resultat måste du %1$sgå till dina läsinställningar%2$s och avmarkera rutan för synlighet i sökmotorer."

#: src/presenters/admin/indexing-notification-presenter.php:117
msgid " We estimate this will take less than a minute."
msgstr " Vi uppskattar att detta kommer att ta mindre än en minut."

#: src/presenters/admin/indexing-notification-presenter.php:121
msgid " We estimate this will take a couple of minutes."
msgstr " Vi uppskattar att detta kommer att ta några minuter."

#. translators: 1: Link to article about indexation command, 2: Anchor closing
#. tag, 3: Link to WP CLI.
#: src/presenters/admin/indexing-notification-presenter.php:136
msgid "%1$sRun the indexation process on your server%2$s using %3$sWP CLI%2$s."
msgstr "%1$sKör indexeringsprocessen på din server%2$s med hjälp av %3$sWP CLI%2$s."

#: src/presenters/admin/indexing-notification-presenter.php:124
msgid " We estimate this could take a long time, due to the size of your site. As an alternative to waiting, you could:"
msgstr " Vi uppskattar att detta kan ta lång tid på grund av storleken på din webbplats. Som ett alternativ till att vänta kan du:"

#. translators: %1$s: link to help article about solving table issue. %2$s: is
#. anchor closing.
#: src/presenters/admin/migration-error-presenter.php:52
msgid "Please read %1$sthis help article%2$s to find out how to resolve this problem."
msgstr "Läs %1$sdenna hjälpartikel%2$s för att ta reda på hur man löser detta problem."

#: inc/class-wpseo-replace-vars.php:1486
msgid "Term hierarchy"
msgstr "Termhierarki"

#: admin/views/class-yoast-feature-toggles.php:177
msgid "REST API: Head endpoint"
msgstr "REST API: Huvudändpunkt"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:181
msgid "This %1$s REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use %1$s for all their SEO meta output."
msgstr "Denna REST API-ändpunkt hos %1$s ger dig all metadata du behöver för en viss URL. Det gör det mycket enkelt för ”headless” WordPress-webbplatser att använda %1$s för presentation av alla SEO-metadata."

#: inc/class-wpseo-replace-vars.php:1486
msgid "Replaced with the term ancestors hierarchy"
msgstr "Ersätts med hierarkin för termens högsta överordnade term"

#. translators: 1: link open tag; 2: link close tag.
#: src/services/health-check/default-tagline-reports.php:63
msgid "%1$sYou can change the tagline in the customizer%2$s."
msgstr "%1$sDu kan ändra slogan i anpassaren%2$s."

#: src/services/health-check/default-tagline-reports.php:32
msgid "You are using a custom tagline or an empty one."
msgstr "Du använder en anpassad slogan eller en tom."

#: src/services/health-check/postname-permalink-reports.php:30
msgid "Your permalink structure includes the post name"
msgstr "Din permalänks-struktur innehåller inläggsnamnet"

#: src/services/health-check/default-tagline-reports.php:30
msgid "You changed the default WordPress tagline"
msgstr "Du ändrade WordPress standardslogan"

#: src/services/health-check/page-comments-reports.php:32
msgid "Comments on your posts are displayed on a single page. This is just like we'd suggest it. You're doing well!"
msgstr "Kommentarer på dina inlägg visas på en enskild sida. Detta är precis det som vi skulle föreslå. Du gör det bra!"

#: src/services/health-check/postname-permalink-reports.php:32
msgid "You do have your postname in the URL of your posts and pages."
msgstr "Du har ditt inläggsnamn i URL:erna för dina inlägg och sidor."

#. translators: %s expands to '/%postname%/'
#: src/services/health-check/postname-permalink-reports.php:58
msgid "It's highly recommended to have your postname in the URL of your posts and pages. Consider setting your permalink structure to %s."
msgstr "Det rekommenderas starkt att du har ditt inläggsnamn i URL:erna för dina inlägg och sidor. Överväg att ställa in strukturen för dina permalänkar till %s."

#: src/services/health-check/postname-permalink-reports.php:43
msgid "You do not have your postname in the URL of your posts and pages"
msgstr "Du har inte ditt inläggsnamn i URL:erna för dina inlägg och sidor"

#: src/services/health-check/default-tagline-reports.php:45
msgid "You still have the default WordPress tagline. Even an empty one is probably better."
msgstr "Du har fortfarande WordPress standardslogan. Till och med en tom är förmodligen bättre."

#: src/services/health-check/default-tagline-reports.php:43
msgid "You should change the default WordPress tagline"
msgstr "Du borde ändra WordPress standardslogan"

#: src/services/health-check/page-comments-reports.php:45
msgid "Comments on your posts break into multiple pages. As this is not needed in 999 out of 1000 cases, we recommend you disable it. To fix this, uncheck \"Break comments into pages...\" on the Discussion Settings page."
msgstr "Kommentarer på dina inlägg delas upp på flera sidor. Eftersom detta inte behövs i 999 av 1000 fall rekommenderar vi att du inaktiverar det. För att åtgärda detta, avmarkera ”Dela upp kommentarer på sidor…” på sidan ”Diskussionsinställningar”."

#: src/helpers/post-helper.php:112
msgid "No title"
msgstr "Ingen rubrik"

#: src/services/health-check/page-comments-reports.php:43
msgid "Comments break into multiple pages"
msgstr "Kommentarer delas upp på flera sidor"

#: src/services/health-check/page-comments-reports.php:30
msgid "Comments are displayed on a single page"
msgstr "Kommentarer visas på en enskild sida"

#. translators: 1: Opening tag of the link to the discussion settings page, 2:
#. Link closing tag.
#: src/services/health-check/page-comments-reports.php:58
msgid "%1$sGo to the Discussion Settings page%2$s"
msgstr "%1$sGå till sidan för diskussionsinställningar%2$s"

#. translators: 1: Start of a paragraph beginning with the Yoast icon, 2:
#. Expands to 'Yoast SEO', 3: Paragraph closing tag.
#: src/services/health-check/report-builder.php:201
msgid "%1$sThis was reported by the %2$s plugin%3$s"
msgstr "%1$sDetta rapporterades av tillägget %2$s%3$s"

#: admin/metabox/class-metabox.php:194
msgid "If you want to apply advanced <code>meta</code> robots settings for this page, please define them in the following field."
msgstr "Om du vill tillämpa avancerade inställningar för <code>meta</code>-robotar för denna sida, definiera dem i följande fält."

#. translators: 1: Link start tag to the Firefox website, 2: Link start tag to
#. the Chrome website, 3: Link start tag to the Edge website, 4: Link closing
#. tag.
#: admin/metabox/class-metabox.php:150 admin/taxonomy/class-taxonomy.php:113
msgid "The browser you are currently using is unfortunately rather dated. Since we strive to give you the best experience possible, we no longer support this browser. Instead, please use %1$sFirefox%4$s, %2$sChrome%4$s or %3$sMicrosoft Edge%4$s."
msgstr "Webbläsaren du för närvarande använder är tyvärr ganska omodern. Eftersom vi strävar efter att ge dig den bästa möjliga upplevelsen stöder vi inte längre denna webbläsare. Använd istället %1$sFirefox%4$s, %2$sChrome%4$s eller %3$sMicrosoft Edge%4$s."

#. translators: %1$s expands to Yoast SEO academy
#. translators: %1$s expands to "Yoast SEO academy".
#: src/presenters/admin/sidebar-presenter.php:168 js/dist/general-page.js:3
#: js/dist/new-settings.js:3 js/dist/support.js:3
msgid "Check out %1$s"
msgstr "Kolla in %1$s"

#: src/presenters/admin/sidebar-presenter.php:151 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "Learn SEO"
msgstr "Lär dig SEO"

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:72
msgid "Your %1$s settings:"
msgstr "Dina %1$s-inställningar:"

#: src/presenters/admin/sidebar-presenter.php:161 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "We have both free and premium online courses to learn everything you need to know about SEO."
msgstr "Vi har både gratis- och premiumkurser online för att lära dig allt du behöver veta om SEO."

#. translators: %1$s expands to Yoast SEO academy, which is a clickable link.
#. translators: %1$s expands to "Yoast SEO" academy, which is a clickable link.
#: src/presenters/admin/sidebar-presenter.php:159 js/dist/general-page.js:2
#: js/dist/new-settings.js:2 js/dist/support.js:2
msgid "Want to learn SEO from Team Yoast? Check out our %1$s!"
msgstr "Vill du lära dig SEO från Team Yoast? Kolla in vår %1$s!"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-import.php:39
msgid "%s settings to import:"
msgstr "%s inställningar att importera:"

#. translators: 1: expands to Yoast SEO, 2: expands to Import settings.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings from another %1$s installation by pasting them here and clicking \"%2$s\"."
msgstr "Importera inställningar från en annan %1$s-installation genom att klistra in dem här och klicka på ”%2$s”."

#: admin/metabox/class-metabox.php:427 js/dist/block-editor.js:567
#: js/dist/elementor.js:530 js/dist/new-settings.js:28
#: js/dist/new-settings.js:35 js/dist/new-settings.js:39
#: js/dist/new-settings.js:68 js/dist/new-settings.js:266
msgid "Schema"
msgstr "Schema"

#: admin/admin-settings-changed-listener.php:85
msgid "Settings saved."
msgstr "Inställningar sparade."

#. translators: %d expands the amount of hidden notifications.
#: admin/views/partial-notifications-errors.php:25
#: admin/views/partial-notifications-warnings.php:25
msgid "You have %d hidden notification:"
msgid_plural "You have %d hidden notifications:"
msgstr[0] "Du har %d dold avisering:"
msgstr[1] "Du har %d dolda aviseringar:"

#. translators: Hidden accessibility text.
#: admin/views/partial-notifications-template.php:47
msgid "Show this item."
msgstr "Visa detta objekt."

#. translators: Hidden accessibility text.
#: admin/views/partial-notifications-template.php:39
msgid "Hide this item."
msgstr "Dölj detta objekt."

#: src/helpers/score-icon-helper.php:84
msgid "Focus keyphrase not set"
msgstr "Fokusnyckelordsfras har inte ställts in"

#. translators: %1$s: amount of errors, %2$s: the admin page title
#: admin/class-yoast-input-validation.php:65
msgid "The form contains %1$s error. %2$s"
msgid_plural "The form contains %1$s errors. %2$s"
msgstr[0] "Formuläret innehåller %1$s fel. %2$s"
msgstr[1] "Formuläret innehåller %1$s fel. %2$s"

#. translators: %s expands to the score
#: admin/statistics/class-statistics-service.php:216
#: admin/statistics/class-statistics-service.php:221
#: admin/statistics/class-statistics-service.php:226
msgid "Posts with the SEO score: %s"
msgstr "Inlägg med SEO-poäng: %s"

#. translators: %s expands to the SEO score
#: inc/class-wpseo-rank.php:170 inc/class-wpseo-rank.php:175
#: inc/class-wpseo-rank.php:180 inc/class-wpseo-rank.php:185
#: inc/class-wpseo-rank.php:190
msgid "SEO: %s"
msgstr "SEO: %s"

#: inc/class-wpseo-rank.php:171
msgid "No Focus Keyphrase"
msgstr "Ingen fokusnyckelordsfras"

#. translators: %s: expends to Yoast SEO
#: admin/class-admin.php:354
msgid "%s video tutorial"
msgstr "Videohandledning för %s"

#: inc/class-wpseo-rank.php:191
msgid "Post Noindexed"
msgstr "Inlägget indexeras inte (noindex)"

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:39
msgid "To view your current crawl errors, %1$splease visit Google Search Console%2$s."
msgstr "För att visa dina nuvarande genomsökningsfel, %1$sbesök Google Search Console%2$s."

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:32
msgid "Google has discontinued its Crawl Errors API. Therefore, any possible crawl errors you might have cannot be displayed here anymore. %1$sRead our statement on this for further information%2$s."
msgstr "Google har stängt ner sitt Crawl Errors API. Därför går det inte längre att här visa eventuella fel från indexeringen av din webbplats. %1$sLäs vidare i vårt uttalande om detta%2$s."

#: src/integrations/admin/first-time-configuration-integration.php:480
#: src/integrations/admin/first-time-configuration-integration.php:493
#: js/dist/new-settings.js:336
msgid "Organization"
msgstr "Organisation"

#. translators: %1$s is a link start tag to the Search Appearance settings,
#. %2$s is the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:66
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "Du har tidigare ställt in att din webbplats representerar en person. Vi har förbättrat vår funktionalitet kring Schema och Knowledge Graph, så du bör gå in och %1$sslutföra dessa inställningar%2$s."

#: src/generators/schema/article.php:141
msgid "Uncategorized"
msgstr "Okategoriserad"

#: admin/class-admin.php:317
#: src/user-meta/framework/additional-contactmethods/wikipedia.php:28
msgid "(if one exists)"
msgstr "(om någon finns)"

#: admin/class-admin.php:317
#: src/user-meta/framework/additional-contactmethods/wikipedia.php:28
msgid "Wikipedia page about you"
msgstr "Wikipedia-sida om dig"

#: admin/class-admin.php:316
#: src/user-meta/framework/additional-contactmethods/youtube.php:28
msgid "YouTube profile URL"
msgstr "URL för YouTube-profil"

#: admin/class-admin.php:314
#: src/user-meta/framework/additional-contactmethods/tumblr.php:28
msgid "Tumblr profile URL"
msgstr "URL för Tumblr-profil"

#: admin/class-admin.php:313
#: src/user-meta/framework/additional-contactmethods/soundcloud.php:28
msgid "SoundCloud profile URL"
msgstr "URL för SoundCloud-profil"

#: admin/class-admin.php:311
#: src/user-meta/framework/additional-contactmethods/myspace.php:28
msgid "MySpace profile URL"
msgstr "URL för MySpace-profil"

#: admin/class-admin.php:312
#: src/user-meta/framework/additional-contactmethods/pinterest.php:28
msgid "Pinterest profile URL"
msgstr "URL för Pinterest-profil"

#: admin/class-admin.php:310
#: src/user-meta/framework/additional-contactmethods/linkedin.php:28
msgid "LinkedIn profile URL"
msgstr "URL för LinkedIn-profil"

#: admin/class-admin.php:309
#: src/user-meta/framework/additional-contactmethods/instagram.php:28
msgid "Instagram profile URL"
msgstr "URL för Instagram-profil"

#: inc/class-my-yoast-api-request.php:140
msgid "No JSON object was returned."
msgstr "Inget JSON-objekt returnerades."

#. translators: Hidden accessibility text.
#: src/integrations/admin/link-count-columns-integration.php:149
msgid "Received internal links"
msgstr "Mottagna interna länkar"

#. translators: Hidden accessibility text.
#: src/integrations/admin/link-count-columns-integration.php:141
msgid "Outgoing internal links"
msgstr "Utgående interna länkar"

#: admin/class-meta-columns.php:132 js/dist/block-editor.js:164
#: js/dist/classic-editor.js:149 js/dist/editor-modules.js:286
#: js/dist/elementor.js:500 js/dist/wincher-dashboard-widget.js:117
msgid "Keyphrase"
msgstr "Nyckelordsfras"

#. translators: 1: Yoast SEO.
#: src/services/health-check/links-table-reports.php:87
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "För att denna funktion ska fungera behöver %1$s skapa en tabell i din databas. Vi kunde inte skapa denna tabell automatiskt."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "Det går inte att läsa av storleken på %1$s eftersom adressen leder till extern server."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "Av okänd anledning går det inte att läsa av storleken på %1$s."

#. translators: %s expands to the current page number
#: src/generators/breadcrumbs-generator.php:424
msgid "Page %s"
msgstr "Sida %s"

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:24
msgid "Method %1$s() does not exist in class %2$s"
msgstr "Metod %1$s() finns inte i klass %2$s"

#: admin/class-export.php:54
msgid "You do not have the required rights to export settings."
msgstr "Du har inte rättigheterna som krävs för att exportera inställningar."

#: admin/import/class-import-settings.php:85
msgid "No settings found."
msgstr "Inga inställningar hittades."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:26
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "Exportera dina %1$s-inställningar här för att kopiera dem på en annan webbplats."

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:226
msgid "With %s, you can easily create such redirects."
msgstr "Med %s kan du enkelt skapa sådana omdirigeringar."

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "Import av inställningar stöds bara på servrar som kör PHP 5.3 eller högre."

#. translators: %1$s expands to Import settings
#: admin/class-export.php:61
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "Kopiera alla dessa inställningar till fliken %1$s på en annan webbplats och klicka på ”%1$s” där."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:97
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "Dessa är inställningar för tillägget %1$s av %2$s"

#: admin/class-admin-init.php:345
msgid "WARNING:"
msgstr "VARNING:"

#: admin/class-admin-init.php:354
msgid "Learn about why permalinks are important for SEO."
msgstr "Lär dig om varför permalänkar är viktiga för SEO."

#. translators: %s expands to Yoast WooCommerce SEO
#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to "Yoast SEO" Premium
#: admin/class-premium-upsell-admin-block.php:167
#: admin/class-premium-upsell-admin-block.php:179 js/dist/ai-consent.js:11
#: js/dist/ai-consent.js:12 js/dist/ai-generator.js:11
#: js/dist/ai-generator.js:12 js/dist/block-editor.js:26
#: js/dist/block-editor.js:27 js/dist/classic-editor.js:11
#: js/dist/classic-editor.js:12 js/dist/editor-modules.js:319
#: js/dist/editor-modules.js:320 js/dist/elementor.js:12
#: js/dist/elementor.js:13 js/dist/externals-components.js:192
#: js/dist/externals-components.js:193 js/dist/general-page.js:13
#: js/dist/general-page.js:14 js/dist/integrations-page.js:47
#: js/dist/integrations-page.js:48 js/dist/new-settings.js:13
#: js/dist/new-settings.js:14 js/dist/redirects.js:11 js/dist/redirects.js:12
#: js/dist/support.js:13 js/dist/support.js:14
msgid "Upgrade to %s"
msgstr "Uppgradera till %s"

#. translators: %1$s and %2$s expand to <em> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:348
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "Om du ändrar dina permalänkar riskerar du att allvarligt påverka webbplatsen synlighet för sökmotorer. Man ska nästan %1$saldrig%2$s göra detta på en aktiv webbplats."

#: admin/views/tabs/network/features.php:95
#: admin/views/tabs/network/integrations.php:82
#: src/integrations/admin/crawl-settings-integration.php:251
msgid "Disable"
msgstr "Inaktivera"

#: admin/views/tabs/network/features.php:94
#: admin/views/tabs/network/integrations.php:81
#: src/integrations/admin/crawl-settings-integration.php:249
msgid "Allow Control"
msgstr "Tillåt kontroll"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/crawl-settings.php:24
#: admin/views/tabs/network/features.php:27
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "Denna flik låter dig selektivt inaktivera funktioner i %s för alla webbsidor i nätverket. Som standard är alla funktioner aktiverade, vilket låter sidadministratörer att själva avgöra om de vill använda en funktion eller inte för sin webbplats. Om du inaktiverar en funktion här kommer administratörerna för olika webbplatser inte att kunna använda den funktionen överhuvudtaget."

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:161
msgid "%s is a required feature toggle argument."
msgstr "%s är ett obligatoriskt argument för funktionsväxling."

#: admin/class-yoast-form.php:1064 js/dist/general-page.js:46
msgid "This feature has been disabled by the network admin."
msgstr "Denna funktion har inaktiverats av nätverksadministratören."

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:197
msgid "Focus keyphrase not set."
msgstr "Ingen fokusnyckelordsfras har angetts."

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-popup.php:81
#: admin/watchers/class-slug-change-watcher.php:230
msgid "Get %s"
msgstr "Skaffa %s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:90
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "Både %1$s och %2$s hanterar SEO på din webbplats. Att köra två SEO-tillägg samtidigt är skadlig."

#: inc/options/class-wpseo-option-titles.php:949
msgid "Colon"
msgstr "Kolon"

#: inc/class-wpseo-admin-bar-menu.php:887
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "Det finns en ny avisering."
msgstr[1] "Det finns nya aviseringar."

#. translators: %d expands to the number of minute/minutes.
#. translators: %d expands to the number of minutes.
#: src/integrations/blocks/structured-data-blocks.php:146
#: js/dist/how-to-block.js:8 js/dist/how-to-block.js:14
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minut"
msgstr[1] "%d minuter"

#. translators: %d expands to the number of hour/hours.
#. translators: %d expands to the number of hours.
#: src/integrations/blocks/structured-data-blocks.php:139
#: js/dist/how-to-block.js:7 js/dist/how-to-block.js:13
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d timme"
msgstr[1] "%d timmar"

#. translators: %d expands to the number of day/days.
#. translators: %d expands to the number of days.
#: src/integrations/blocks/structured-data-blocks.php:132
#: js/dist/block-editor.js:143 js/dist/classic-editor.js:128
#: js/dist/editor-modules.js:265 js/dist/elementor.js:479
#: js/dist/how-to-block.js:6 js/dist/how-to-block.js:12
#: js/dist/wincher-dashboard-widget.js:43
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dag"
msgstr[1] "%d dagar"

#: blocks/structured-data-blocks/how-to/block.json
msgctxt "block description"
msgid "Create a How-to guide in an SEO-friendly way. You can only use one How-to block per post."
msgstr "Skapa en Hur-gör-man-guide på ett SEO-vänligt sätt. Du kan endast använda ett Hur-gör-man-block per inlägg."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:37
msgid "%1$s Structured Data Blocks"
msgstr "%1$s strukturerade datablock"

#: inc/class-wpseo-admin-bar-menu.php:439
msgid "Check links to this URL"
msgstr "Kontrollera länkar till denna URL"

#: src/integrations/blocks/structured-data-blocks.php:197
#: js/dist/how-to-block.js:5 js/dist/how-to-block.js:16
msgid "Time needed:"
msgstr "Tid som behövs:"

#: admin/pages/network.php:31
msgid "Restore Site"
msgstr "Återställ webbplats"

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:208
msgid "Error: %s"
msgstr "Fel: %s"

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "borttaget"

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:168
msgid "Site with ID %d not found."
msgstr "Webbplats med ID %d hittades inte."

#: admin/class-yoast-network-admin.php:159
msgid "No site has been selected to restore."
msgstr "Ingen webbplats har valts för att återställa."

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "Nätverksinställningar"

#: inc/class-wpseo-admin-bar-menu.php:510
msgid "How to"
msgstr "Hur man gör"

#: admin/class-yoast-network-admin.php:120
msgid "You are not allowed to modify unregistered network settings."
msgstr "Du saknar behörighet att modifiera oregistrerade nätverksinställningar."

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:206
msgid "Success: %s"
msgstr "Klart: %s"

#: admin/class-yoast-network-admin.php:276
msgid "You are not allowed to perform this action."
msgstr "Du har inte behörighet att utföra denna åtgärd."

#: inc/class-wpseo-replace-vars.php:1475
msgid "The site's tagline"
msgstr "Webbplatsens slogan"

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:152
msgid "Not all required fields are given. Missing field %1$s"
msgstr "Alla obligatoriska fält är inte ifyllda. Fältet %1$s saknas"

#: inc/class-wpseo-replace-vars.php:1489 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Current year"
msgstr "Nuvarande år"

#: inc/class-wpseo-replace-vars.php:1520 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Page"
msgstr "Sida"

#: inc/class-wpseo-replace-vars.php:1475 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1 js/dist/new-settings.js:33
#: js/dist/new-settings.js:318
msgid "Tagline"
msgstr "Slogan"

#: inc/class-wpseo-replace-vars.php:1528
msgid "description (custom taxonomy)"
msgstr "beskrivning (anpassad taxonomi)"

#: inc/class-wpseo-replace-vars.php:1527
msgid "(custom taxonomy)"
msgstr "(anpassad taxonomi)"

#: inc/class-wpseo-replace-vars.php:1526
msgid "(custom field)"
msgstr "(anpassat fält)"

#: inc/class-wpseo-replace-vars.php:1525
msgid "Term404"
msgstr "Term404"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Pagenumber"
msgstr "Sidnummer"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Pagetotal"
msgstr "Sidtotal"

#: inc/class-wpseo-replace-vars.php:1519
msgid "User description"
msgstr "Användarbeskrivning"

#: inc/class-wpseo-replace-vars.php:1517 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "ID"
msgstr "ID"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Modified"
msgstr "Ändrad"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Post type (plural)"
msgstr "Inläggstyp (plural)"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Post type (singular)"
msgstr "Inläggstyp (singular)"

#: inc/class-wpseo-replace-vars.php:1487 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1 js/dist/new-settings.js:35
msgid "Separator"
msgstr "Avgränsare"

#: inc/class-wpseo-replace-vars.php:1485 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Search phrase"
msgstr "Sökfras"

#: inc/class-wpseo-replace-vars.php:1484
msgid "Term title"
msgstr "Termrubrik"

#: inc/class-wpseo-replace-vars.php:1483 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Term description"
msgstr "Termbeskrivning"

#: inc/class-wpseo-replace-vars.php:1482 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Tag description"
msgstr "Etikettbeskrivning"

#: inc/class-wpseo-replace-vars.php:1481 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Category description"
msgstr "Kategoribeskrivning"

#: inc/class-wpseo-replace-vars.php:1480 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Primary category"
msgstr "Huvudkategori"

#: inc/class-wpseo-replace-vars.php:1479 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Category"
msgstr "Kategori"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Tag"
msgstr "Etikett"

#: inc/class-wpseo-replace-vars.php:1477 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Excerpt only"
msgstr "Endast utdrag"

#: inc/class-wpseo-replace-vars.php:1476 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Excerpt"
msgstr "Utdrag"

#: inc/class-wpseo-replace-vars.php:1474 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Site title"
msgstr "Sidrubrik"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Archive title"
msgstr "Arkivrubrik"

#: inc/class-wpseo-replace-vars.php:1472 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Parent title"
msgstr "Överordnad rubrik"

#: inc/class-wpseo-replace-vars.php:1470 js/dist/elementor.js:530
#: js/dist/externals-redux.js:1
msgid "Date"
msgstr "Datum"

#: inc/class-wpseo-admin-bar-menu.php:608
msgid "Get Yoast SEO Premium"
msgstr "Skaffa Yoast SEO Premium"

#: admin/watchers/class-slug-change-watcher.php:220
msgid "Make sure you don't miss out on traffic!"
msgstr "Se till att du inte missar trafik!"

#. translators: %1$s expands to the translated name of the post type.
#. translators: %1$s expands to the translated name of the term.
#: admin/watchers/class-slug-change-watcher.php:89
#: admin/watchers/class-slug-change-watcher.php:112
msgid "You just deleted a %1$s."
msgstr "Du tog just bort en %1$s."

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:67
msgid "You just trashed a %1$s."
msgstr "Du slängde just en %1$s."

#: admin/watchers/class-slug-change-watcher.php:224
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "Du bör skapa en omdirigering för att se till att dina besökare inte får ett 404-fel när de klickar på den URL som inte längre fungerar."

#: admin/watchers/class-slug-change-watcher.php:90
#: admin/watchers/class-slug-change-watcher.php:113
msgid "Search engines and other websites can still send traffic to your deleted content."
msgstr "Sökmotorer och andra webbplatser kan fortfarande skicka trafik till ditt borttagna innehåll."

#: inc/class-wpseo-replace-vars.php:1523
msgid "Caption"
msgstr "Bildtext"

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:132
msgid "Cleanup of %s data failed."
msgstr "Rensningen av %s data misslyckades."

#: admin/class-bulk-editor-list-table.php:1037
msgid "Content Type"
msgstr "Innehållstyp"

#. translators: Hidden accessibility text.
#: admin/class-bulk-editor-list-table.php:429
msgid "Filter by content type"
msgstr "Filtrera efter innehållstyp"

#: admin/class-bulk-editor-list-table.php:411
msgid "Show All Content Types"
msgstr "Visa alla innehållstyper"

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:259
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "Importfunktionen %s använder temporära databastabeller. Det verkar som om din WordPress-installation inte har möjlighet att göra detta, kontakta ditt webbhotell."

#: inc/class-wpseo-replace-vars.php:1473
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "Ersatt med den vanliga rubriken för ett arkiv som genereras av WordPress"

#: admin/views/tabs/tool/import-seo.php:126
msgid "Clean"
msgstr "Rensa"

#: admin/views/tabs/tool/import-seo.php:117
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "När du är säker på att din webbplats är OK så kan du rensa den. Detta kommer ta bort alla originaldata."

#: admin/views/tabs/tool/import-seo.php:115
msgid "Step 5: Clean up"
msgstr "Steg 5: Rensa"

#: admin/views/tabs/tool/import-seo.php:95
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "Kontrollera dina sidor och inlägg så att metadatan importerades."

#: admin/views/tabs/tool/import-seo.php:93
msgid "Step 3: Check your data"
msgstr "Steg 3: Kontrollera dina data"

#: admin/views/tabs/tool/import-seo.php:62
msgid "Step 2: Import"
msgstr "Steg 2: Importera"

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "%s data hittades."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "%s data hittades inte."

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "%s data importerades."

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "%s data har tagits bort."

#: admin/statistics/class-statistics-service.php:229
msgid "Posts that should not show up in search results"
msgstr "Inlägg som inte visas i sökresultat"

#: admin/views/tabs/tool/import-seo.php:51
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "Vi har upptäckt data från en eller flera SEO-tillägg på din webbplats. Följ stegen nedan för att importera detta:"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:22
msgid "%s did not detect any plugin data from plugins it can import from."
msgstr "%s upptäckte inte någon tilläggsdata från tillägg som det kan importera från."

#: admin/views/tabs/tool/import-seo.php:55
msgid "Step 1: Create a backup"
msgstr "Steg 1: Skapa en säkerhetskopia"

#: admin/views/tabs/tool/import-seo.php:57
msgid "Please make a backup of your database before starting this process."
msgstr "Gör en säkerhetskopia av din databas innan du startar denna process."

#. translators: 1: expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:67
msgid "This will import the post metadata like SEO titles and descriptions into your %1$s metadata. It will only do this when there is no existing %1$s metadata yet. The original data will remain in place."
msgstr "Detta kommer att importera inläggets metadata som SEO-rubriker och beskrivningar till dina metadata för %1$s. Detta görs endast när det inte finns någon existerande metadata för %1$s. De ursprungliga uppgifterna kommer att finnas kvar."

#: admin/views/tabs/tool/import-seo.php:39
msgid "Plugin: "
msgstr "Tillägg: "

#: admin/views/user-profile.php:17
msgid "this author's archives"
msgstr "arkiv för denna författare"

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:16
msgid "Do not allow search engines to show %s in search results."
msgstr "Tillåt inte sökmotorer att visa %s i sökresultat."

#: admin/class-yoast-form.php:960 admin/class-yoast-form.php:1000
#: js/dist/externals/componentsNew.js:766
msgid "On"
msgstr "På"

#. translators: Hidden accessibility text; %s expands to a feature's name.
#. translators: Hidden accessibility text; %s expands to an integration's name.
#: admin/views/tabs/network/features.php:62
#: admin/views/tabs/network/integrations.php:50
msgid "Help on: %s"
msgstr "Hjälp med: %s"

#: admin/class-yoast-form.php:961 admin/class-yoast-form.php:1001
#: admin/views/class-yoast-feature-toggles.php:160
#: js/dist/externals/componentsNew.js:766
msgid "Off"
msgstr "Av"

#: admin/views/class-yoast-feature-toggles.php:140
msgid "Read why XML Sitemaps are important for your site."
msgstr "Läs om varför XML-webbplatskartor är viktiga för din webbplats."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:139
msgid "Enable the XML sitemaps that %s generates."
msgstr "Aktivera XML-webbplatskartan som %s genererar."

#: admin/views/class-yoast-feature-toggles.php:70
msgid "See the XML sitemap."
msgstr "Visa XML-webbplatskartan."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:19
msgid "See who contributed to %1$s."
msgstr "Se vem som bidragit till %1$s."

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:189
msgid "Should search engines follow links on this %1$s?"
msgstr "Ska sökmotorer följa länkar på denna %1$s?"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:82
msgid "Toggle %1$s's XML Sitemap"
msgstr "Slå på/av XML-webbplatskartan för %1$s"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:184
msgid "Default for %2$s, currently: %1$s"
msgstr "Standard för %2$s, för närvarande: %1$s"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:971
msgid "Show %s in search results?"
msgstr "Visa %s i sökresultaten?"

#. translators: %s expands to the post type name.
#: admin/metabox/class-metabox.php:179
msgid "Allow search engines to show this %s in search results?"
msgstr "Tillåt sökmotorer att visa denna/detta %s i sökresultat?"

#. translators: %s: 'Semrush'
#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:67
#: admin/views/class-yoast-integration-toggles.php:78
msgid "%s integration"
msgstr "Integration med %s"

#: admin/views/class-yoast-feature-toggles.php:86
msgid "Discover why readability is important for SEO."
msgstr "Få reda på varför läsbarhet är viktigt för SEO."

#: admin/views/class-yoast-feature-toggles.php:103
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "Få reda på hur grundstensinnehåll kan hjälpa dig förbättra strukturen för din webbplats."

#: admin/views/class-yoast-feature-toggles.php:111
msgid "Find out how the text link counter can enhance your SEO."
msgstr "Få reda på hur textlänkräknaren kan förbättra din SEO."

#: admin/views/class-yoast-feature-toggles.php:78
msgid "Learn how the SEO analysis can help you rank."
msgstr "Lär dig hur SEO-analysen kan hjälpa dig ranka."

#: admin/views/class-yoast-feature-toggles.php:75
#: js/dist/externals-components.js:507 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "SEO analysis"
msgstr "SEO-analys"

#: admin/views/class-yoast-feature-toggles.php:77
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "SEO-analysen visar dig hur du kan förbättra SEO för din text."

#: admin/views/class-yoast-feature-toggles.php:102
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "Funktionen grundstensinnehåll låter dig markera och filtrera grundstensinnehåll på din webbplats."

#: admin/views/class-yoast-feature-toggles.php:85 js/dist/new-settings.js:324
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "Läsbarhetsanalysen visar dig hur du förbättrar strukturen och stilen för din text."

#: admin/views/class-yoast-feature-toggles.php:110
msgid "The text link counter helps you improve your site structure."
msgstr "Textlänkräknaren hjälper dig förbättra strukturen för din webbplats."

#. Author URI of the plugin
#: wp-seo.php
msgid "https://yoa.st/1uk"
msgstr "https://yoa.st/1uk"

#. Plugin URI of the plugin
#: wp-seo.php
msgid "https://yoa.st/1uj"
msgstr "https://yoa.st/1uj"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:130
msgid "Latest blog posts on %1$s"
msgstr "Senaste blogginläggen på %1$s"

#: src/helpers/first-time-configuration-notice-helper.php:64
msgid "First-time SEO configuration"
msgstr "Startkonfiguration för SEO"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:28
msgid "You cannot create a %s file."
msgstr "Du kan inte skapa filen %s."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:49 admin/views/tool-file-editor.php:76
msgid "You cannot edit the %s file."
msgstr "Du kan inte redigera filen %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:65
msgid "Updated %s"
msgstr "Uppdaterade %s"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:125
msgid "Create %s file"
msgstr "Skapa filen %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:174 admin/views/tool-file-editor.php:224
msgid "Save changes to %s"
msgstr "Spara ändringar till %s"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:186
msgid "%s file"
msgstr "filen %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:165 admin/views/tool-file-editor.php:215
msgid "Edit the content of your %s:"
msgstr "Redigera innehållet i din %s:"

#. translators: Hidden accessibility text; %1$s expands to the dependency name
#: admin/class-suggested-plugins.php:137
msgid "More information about %1$s"
msgstr "Mer information om %1$s"

#: src/integrations/admin/old-configuration-integration.php:38
msgid "Old Configuration Wizard"
msgstr "Gammal konfigurationsguide"

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "Markera de viktigaste %1$s som ”grundstensinnehåll” för att förbättra din webbplatsstruktur. %2$sLäs mer om grundstensinnehåll%3$s."

#. translators: Hidden accessibility text.
#: admin/class-admin-utils.php:79 admin/class-premium-popup.php:83
#: admin/class-premium-upsell-admin-block.php:69 admin/class-yoast-form.php:935
#: admin/watchers/class-slug-change-watcher.php:232
#: src/integrations/admin/workouts-integration.php:215
#: src/integrations/admin/workouts-integration.php:245
#: src/presenters/admin/help-link-presenter.php:76 js/dist/academy.js:9
#: js/dist/ai-consent.js:4 js/dist/ai-consent.js:6 js/dist/ai-generator.js:4
#: js/dist/ai-generator.js:6 js/dist/ai-generator.js:57
#: js/dist/ai-generator.js:272 js/dist/block-editor.js:19
#: js/dist/block-editor.js:21 js/dist/block-editor.js:535
#: js/dist/block-editor.js:536 js/dist/classic-editor.js:4
#: js/dist/classic-editor.js:6 js/dist/editor-modules.js:313
#: js/dist/editor-modules.js:315 js/dist/editor-modules.js:573
#: js/dist/elementor.js:5 js/dist/elementor.js:7
#: js/dist/externals-components.js:21 js/dist/externals-components.js:186
#: js/dist/externals-components.js:188 js/dist/externals-components.js:446
#: js/dist/externals/componentsNew.js:1044 js/dist/externals/helpers.js:6
#: js/dist/externals/relatedKeyphraseSuggestions.js:3
#: js/dist/externals/relatedKeyphraseSuggestions.js:7 js/dist/general-page.js:6
#: js/dist/general-page.js:8 js/dist/general-page.js:30
#: js/dist/integrations-page.js:3 js/dist/integrations-page.js:4
#: js/dist/integrations-page.js:5 js/dist/integrations-page.js:6
#: js/dist/integrations-page.js:7 js/dist/integrations-page.js:8
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:18
#: js/dist/integrations-page.js:19 js/dist/integrations-page.js:20
#: js/dist/integrations-page.js:40 js/dist/integrations-page.js:42
#: js/dist/introductions.js:3 js/dist/new-settings.js:6
#: js/dist/new-settings.js:8 js/dist/plans.js:2 js/dist/redirects.js:4
#: js/dist/redirects.js:6 js/dist/support.js:6 js/dist/support.js:8
#: js/dist/support.js:18
msgid "(Opens in a new browser tab)"
msgstr "(Öppnas i en ny webbläsarflik)"

#: admin/statistics/class-statistics-service.php:77
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "Det går ganska bra för din SEO! Kolla in statistiken:"

#: admin/statistics/class-statistics-service.php:73
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "Du har inga publicerade inlägg, dina SEO-poäng kommer att visas här när du gör ditt första inlägg!"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:210
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "Inlägg %1$sutan%2$s en fokusnyckelordsfras"

#: admin/class-yoast-dashboard-widget.php:133
msgid "Read more like this on our SEO blog"
msgstr "Läs mer om sådant här på vår SEO-blogg"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:298
msgid "Filter by Readability Score"
msgstr "Filtrera efter läsbarhetspoäng"

#: admin/class-meta-columns.php:302
msgid "All Readability Scores"
msgstr "Alla läsbarhetspoäng"

#. translators: %1$s expands to Yoast
#: src/presenters/admin/sidebar-presenter.php:34
msgid "%1$s recommendations for you"
msgstr "Rekommendationer för dig från %1$s"

#. translators: %s expands to the readability score
#: inc/class-wpseo-rank.php:207 inc/class-wpseo-rank.php:212
#: inc/class-wpseo-rank.php:217 inc/class-wpseo-rank.php:222
msgid "Readability: %s"
msgstr "Läsbarhet: %s"

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:97
msgid "Request method %1$s is not valid."
msgstr "Metoden %1$s är inte giltig för begäran."

#: admin/views/class-yoast-feature-toggles.php:108 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "Text link counter"
msgstr "Textlänkräknare"

#: admin/class-meta-columns.php:122 admin/class-meta-columns.php:124
#: admin/taxonomy/class-taxonomy-columns.php:92
#: admin/taxonomy/class-taxonomy-columns.php:93
msgid "Readability score"
msgstr "Läsbarhetspoäng"

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:64
msgid "%s Columns"
msgstr "%s-kolumner"

#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:100
#: js/dist/externals-components.js:20 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "Cornerstone content"
msgstr "Grundstensinnehåll"

#: admin/class-yoast-form.php:149 admin/class-yoast-form.php:154
#: js/dist/general-page.js:42 js/dist/new-settings.js:17
msgid "Save changes"
msgstr "Spara ändringar"

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:87
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "Tillägget %2$s ändrar din webbplats output och skiljer genom det mellan sökmotorer och vanliga användare, en process som kallas cloaking. Vi rekommenderar starkt att du inaktiverar det."

#: src/presenters/admin/sidebar-presenter.php:92 js/dist/ai-consent.js:7
#: js/dist/ai-generator.js:7 js/dist/block-editor.js:22
#: js/dist/classic-editor.js:7 js/dist/editor-modules.js:136
#: js/dist/elementor.js:8 js/dist/externals-components.js:143
#: js/dist/general-page.js:9 js/dist/integrations-page.js:43
#: js/dist/new-settings.js:9 js/dist/redirects.js:7 js/dist/support.js:9
msgid "24/7 support"
msgstr "Support 24/7"

#: admin/class-premium-popup.php:89 js/dist/block-editor.js:279
#: js/dist/block-editor.js:529 js/dist/classic-editor.js:264
#: js/dist/classic-editor.js:514 js/dist/elementor.js:81
#: js/dist/elementor.js:530 js/dist/externals-components.js:486
#: js/dist/externals-components.js:488
msgid "1 year free support and updates included!"
msgstr "1 års fri support och uppdateringar ingår!"

#: admin/class-admin.php:355
msgid "Scroll to see the table content."
msgstr "Rulla för att se innehållet i tabellen."

#: admin/class-bulk-editor-list-table.php:922
msgid "Save all"
msgstr "Spara allt"

#: admin/class-bulk-editor-list-table.php:921
msgid "Save"
msgstr "Spara"

#: admin/views/partial-notifications-warnings.php:22 js/dist/general-page.js:30
msgid "No new notifications."
msgstr "Inga nya aviseringar."

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:272
msgid "%1$s, Author at %2$s"
msgstr "%1$s, författare på %2$s"

#: inc/class-wpseo-replace-vars.php:1518 js/dist/general-page.js:42
msgid "Name"
msgstr "Namn"

#: admin/views/tool-import-export.php:89
msgid "Export settings"
msgstr "Exportera inställningar"

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:166
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "Vi har märkt att du har använt %1$s ett tag nu, vi hoppas att du gillar det! Vi skulle bli glada om du kunde %2$sge oss ett 5-stjärnigt betyg på WordPress.org%3$s!"

#: admin/class-product-upsell-notice.php:181
msgid "Please don't show me this notification anymore"
msgstr "Visa inte detta meddelande igen"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast help center, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:174
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "Om du upplever problem, %1$sskicka en felrapport%2$s så ska vi göra vårt bästa för att hjälpa dig."

#. translators: %1$s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template, %2$s: expands to
#. 'HelpScout beacon'
#: admin/class-admin.php:349
msgid "Warning: the variable %1$s cannot be used in this template. See the %2$s for more info."
msgstr "Varning: variabeln %1$s kan inte användas i denna mall. Se %2$s för mer information."

#: admin/class-bulk-editor-list-table.php:829
msgid "(no title)"
msgstr "(ingen rubrik)"

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:149
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "Visste du förresten att vi också har ett %1$sPremium-tillägg%2$s? Det erbjuder avancerade funktioner, så som en hanterare för omdirigeringar och stöd för flera nyckelordsfraser. Det ingår också personlig support 24/7."

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:150
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "Administratörsmenyn %1$s innehåller användbara länkar till tredje-parts-verktyg för att analysera sidor och gör det lätt att se om du har nya notiser."

#: admin/views/class-yoast-feature-toggles.php:147 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324 js/dist/new-settings.js:326
msgid "Admin bar menu"
msgstr "Administratörsmeny"

#: admin/pages/network.php:19 admin/views/tabs/network/features.php:22
msgid "Features"
msgstr "Funktioner"

#: admin/metabox/class-metabox.php:175 js/dist/externals/analysis.js:107
#: js/dist/externals/analysis.js:207
#: js/dist/externals/replacementVariableEditor.js:27
#: js/dist/externals/searchMetadataPreviews.js:62 js/dist/new-settings.js:26
#: js/dist/new-settings.js:30 js/dist/new-settings.js:33
#: js/dist/new-settings.js:35 js/dist/new-settings.js:39
#: js/dist/new-settings.js:65 js/dist/new-settings.js:79
#: js/dist/new-settings.js:109 js/dist/new-settings.js:138
#: js/dist/new-settings.js:203 js/dist/new-settings.js:220
#: js/dist/new-settings.js:229 js/dist/new-settings.js:266
msgid "SEO title"
msgstr "SEO-rubrik"

#: inc/options/class-wpseo-option-titles.php:985
msgid "Less than sign"
msgstr "Mindre än-tecken"

#: admin/metabox/class-metabox.php:186 admin/metabox/class-metabox.php:191
#: js/dist/block-editor.js:279 js/dist/classic-editor.js:264
#: js/dist/elementor.js:383
msgid "No"
msgstr "Nej"

#: admin/metabox/class-metabox.php:185 admin/metabox/class-metabox.php:190
#: js/dist/block-editor.js:279 js/dist/classic-editor.js:264
#: js/dist/elementor.js:383
msgid "Yes"
msgstr "Ja"

#: inc/options/class-wpseo-option-titles.php:989
msgid "Greater than sign"
msgstr "Större än-tecken"

#: inc/options/class-wpseo-option-titles.php:981
msgid "Right angle quotation mark"
msgstr "Högervinklat citationstecken"

#: inc/options/class-wpseo-option-titles.php:977
msgid "Left angle quotation mark"
msgstr "Vänstervinklat citationstecken"

#: inc/options/class-wpseo-option-titles.php:973
msgid "Small tilde"
msgstr "Liten tilde"

#: inc/options/class-wpseo-option-titles.php:969
msgid "Vertical bar"
msgstr "Lodrätt streck"

#: inc/options/class-wpseo-option-titles.php:965
msgid "Low asterisk"
msgstr "Låg asterisk"

#: inc/options/class-wpseo-option-titles.php:961
msgid "Asterisk"
msgstr "Asterisk"

#: inc/options/class-wpseo-option-titles.php:957
msgid "Bullet"
msgstr "Punkt"

#: inc/options/class-wpseo-option-titles.php:953
msgid "Middle dot"
msgstr "Mellanpunkt"

#: inc/options/class-wpseo-option-titles.php:945
msgid "Em dash"
msgstr "Brett tankstreck"

#: inc/options/class-wpseo-option-titles.php:941
msgid "En dash"
msgstr "Tankstreck"

#: inc/options/class-wpseo-option-titles.php:937
msgid "Dash"
msgstr "Smalt tankstreck"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "Inläggslista"

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "Navigation för inläggslista"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "Filtrera inläggslistan"

#: src/integrations/admin/menu-badge-integration.php:35 js/dist/academy.js:8
msgid "Premium"
msgstr "Premium"

#: admin/class-admin.php:266
msgid "Get Premium"
msgstr "Skaffa Premium"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:843
msgid "Edit &#8220;%s&#8221;"
msgstr "Redigera ”%s”"

#: admin/views/user-profile.php:47
#: src/user-meta/framework/custom-meta/keyword-analysis-disable.php:109
msgid "Disable SEO analysis"
msgstr "Inaktivera SEO-analys"

#: admin/views/user-profile.php:50
#: src/user-meta/framework/custom-meta/keyword-analysis-disable.php:115
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "Tar bort sektionen nyckelordsfras från metarutan och inaktiverar alla SEO-relaterade förslag."

#: admin/views/partial-notifications-warnings.php:20
#: inc/class-wpseo-admin-bar-menu.php:400 js/dist/general-page.js:30
msgid "Notifications"
msgstr "Aviseringar"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "Gör till primär"

#. translators: Hidden accessibility text; %s: number of notifications.
#: admin/menu/class-admin-menu.php:118 inc/class-wpseo-admin-bar-menu.php:862
#: js/dist/general-page.js:50
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s avisering"
msgstr[1] "%s aviseringar"

#: admin/views/user-profile.php:59
#: src/user-meta/framework/custom-meta/content-analysis-disable.php:109
msgid "Disable readability analysis"
msgstr "Inaktivera läsbarhetsanalys"

#: admin/views/class-yoast-feature-toggles.php:83
#: js/dist/externals-components.js:462 js/dist/new-settings.js:33
#: js/dist/new-settings.js:324
msgid "Readability analysis"
msgstr "Läsbarhetsanalys"

#: admin/metabox/class-metabox-section-readability.php:30
#: inc/class-wpseo-admin-bar-menu.php:256
msgid "Readability"
msgstr "Läsbarhet"

#: admin/statistics/class-statistics-service.php:217
#: inc/class-wpseo-rank.php:140 inc/class-wpseo-rank.php:176
#: inc/class-wpseo-rank.php:208 inc/class-wpseo-rank.php:240
#: js/dist/block-editor.js:534 js/dist/editor-modules.js:310
#: js/dist/editor-modules.js:574 js/dist/elementor.js:84
#: js/dist/externals-components.js:183 js/dist/externals/analysis.js:15
#: js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:1
#: js/dist/term-edit.js:1
msgid "Needs improvement"
msgstr "Behöver förbättras"

#: admin/views/user-profile.php:62
#: src/user-meta/framework/custom-meta/content-analysis-disable.php:115
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "Tar bort sektionen läsbarhetsanalys från metarutan och inaktiverar alla läsbarhetsrelaterade förslag."

#: admin/views/partial-notifications-errors.php:20
#: js/dist/editor-modules.js:176 js/dist/externals-components.js:183
#: js/dist/externals/analysisReport.js:39 js/dist/general-page.js:30
msgid "Problems"
msgstr "Problem"

#: inc/class-wpseo-rank.php:138 js/dist/block-editor.js:534
#: js/dist/editor-modules.js:310 js/dist/editor-modules.js:574
#: js/dist/elementor.js:84 js/dist/externals-components.js:183
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:1
#: js/dist/term-edit.js:1
msgid "Not available"
msgstr "Inte tillgängligt"

#: admin/views/partial-notifications-errors.php:22 js/dist/general-page.js:30
msgid "Good job! We could detect no serious SEO problems."
msgstr "Bra jobbat! Vi kan inte upptäcka några allvarliga SEO-problem."

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:269
msgid "Filter by SEO Score"
msgstr "Filtrera efter SEO-poäng"

#. translators: Hidden accessibility text.
#: admin/class-meta-columns.php:181
msgid "Meta description not set."
msgstr "Metabeskrivning har inte angetts."

#. translators: %1$s is a link start tag to the permalink settings page, %2$s
#. is the link closing tag.
#: src/services/health-check/postname-permalink-reports.php:71
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "Du kan åtgärda detta på %1$sinställningssidan för permalänkar%2$s."

#: admin/menu/class-admin-menu.php:56 js/dist/general-page.js:49
msgid "Dashboard"
msgstr "Adminpanel"

#: admin/views/partial-notifications-errors.php:21 js/dist/general-page.js:30
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "Vi har upptäckt följande problem som påverkar SEO på din webbplats."

#: inc/class-wpseo-replace-vars.php:1480
msgid "Replaced with the primary category of the post/page"
msgstr "Ersatt med den primära kategorin för inlägget/sidan"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:48
msgid "Existing %1$s Title"
msgstr "Befintlig %1$s rubrik"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:50
msgid "New %1$s Title"
msgstr "Ny %1$s rubrik"

#: inc/sitemaps/class-sitemaps-cache-validator.php:301
msgid "Expected an integer as input."
msgstr "Förväntade ett heltal som inmatning."

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "Försöker bygga cachenyckeln till webbplatskartan, men postfix- och prefixkombinationen lämnar för lite utrymme att göra detta. Du begär förmodligen en sida som är långt ifrån det förväntade intervallet."

#: src/integrations/admin/redirects-page-integration.php:75
#: js/dist/redirects.js:15 js/dist/redirects.js:25
msgid "Redirects"
msgstr "Omdirigeringar"

#: src/integrations/admin/crawl-settings-integration.php:241
msgid "Keep"
msgstr "Behåll"

#: src/integrations/admin/crawl-settings-integration.php:242
#: js/dist/externals/relatedKeyphraseSuggestions.js:1
msgid "Remove"
msgstr "Ta bort"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "Primär"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:38
msgid "Primary %s"
msgstr "Primär %s"

#. translators: Hidden accessibility text; %1$s expands to the term title, %2$s
#. to the taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "Gör %1$s primär %2$s"

#: admin/pages/network.php:20 admin/views/tabs/network/integrations.php:22
#: src/integrations/admin/integrations-page.php:131
#: js/dist/integrations-page.js:57
msgid "Integrations"
msgstr "Integrationer"

#: admin/taxonomy/class-taxonomy-columns.php:170
msgid "Term is set to noindex."
msgstr "Termen är inställd på noindex."

#: src/integrations/admin/crawl-settings-integration.php:195
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Enabled"
msgstr "Aktiverad"

#: src/integrations/admin/crawl-settings-integration.php:194
#: src/presenters/admin/light-switch-presenter.php:120
#: js/dist/externals/dashboardFrontend.js:4
msgid "Disabled"
msgstr "Inaktiverad"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1462
msgid "The separator defined in your theme's %s tag."
msgstr "Avgränsaren definierad i ditt temas %s-tagg."

#: inc/class-wpseo-rank.php:139
msgid "No index"
msgstr "Inget index"

#: admin/class-meta-columns.php:114 admin/class-meta-columns.php:116
#: admin/taxonomy/class-taxonomy-columns.php:87
#: admin/taxonomy/class-taxonomy-columns.php:88
#: inc/class-wpseo-admin-bar-menu.php:244
#: js/dist/externals/dashboardFrontend.js:4
msgid "SEO score"
msgstr "SEO-poäng"

#. translators: %s expands to the name of a post type (plural).
#: inc/class-upgrade.php:1555 inc/options/class-wpseo-option-titles.php:309
msgid "%s Archive"
msgstr "%s arkiv"

#: inc/class-wpseo-admin-bar-menu.php:430
msgid "Analyze this page"
msgstr "Analysera denna sida"

#: inc/options/class-wpseo-option-titles.php:280
msgid "Archives for"
msgstr "Arkiv för"

#: admin/statistics/class-statistics-service.php:227
#: inc/class-wpseo-rank.php:142 inc/class-wpseo-rank.php:186
#: inc/class-wpseo-rank.php:218 inc/class-wpseo-rank.php:250
#: js/dist/block-editor.js:534 js/dist/editor-modules.js:310
#: js/dist/editor-modules.js:574 js/dist/elementor.js:84
#: js/dist/externals-components.js:183 js/dist/externals/analysis.js:15
#: js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:1
#: js/dist/term-edit.js:1
msgid "Good"
msgstr "Bra"

#: inc/class-wpseo-admin-bar-menu.php:454
msgid "Google Page Speed Test"
msgstr "Google Page Speed Test"

#: inc/options/class-wpseo-option-titles.php:281
msgid "Home"
msgstr "Hem"

#: inc/options/class-wpseo-option-ms.php:243
msgid "No numeric value was received."
msgstr "Inget numeriskt värde mottogs."

#: inc/class-wpseo-admin-bar-menu.php:636
#: inc/class-wpseo-admin-bar-menu.php:684
msgid "SEO Settings"
msgstr "SEO-inställningar"

#. Author of the plugin
#: wp-seo.php
msgid "Team Yoast"
msgstr "Team Yoast"

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:231
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "Detta måste vara en existerande blogg. Bloggen %s finns inte eller har markerats som borttagen."

#. Plugin Name of the plugin
#: wp-seo.php admin/capabilities/class-capability-manager-integration.php:74
#: src/presenters/meta-description-presenter.php:36 js/dist/block-editor.js:603
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: inc/options/class-wpseo-option-titles.php:282
msgid "You searched for"
msgstr "Du sökte efter"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:274
msgid "You searched for %s"
msgstr "Du sökte efter %s"

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:208
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$s är inte ett giltigt val för vem som ska ha tillgång till inställningarna för %2$s. Värdet har återställts till det ursprungliga."

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:524
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "Installationen av tillägget %1$s är inte färdig. Se %2$sinstallationsinstruktionerna%3$s."

#: inc/options/class-wpseo-option-ms.php:227
#: inc/options/class-wpseo-option-ms.php:243
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "Inställningen för standard-bloggens ID måste vara det numeriska blogg-ID:et för den blogg du vill ha som standard-blogg."

#. Description of the plugin
#: wp-seo.php
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "Den första riktiga allt-i-ett SEO-lösningen för WordPress, inklusive innehållsanalys på sidor, XML webbplatskartor och mycket mer."

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:277
msgid "The post %1$s appeared first on %2$s."
msgstr "Inlägget %1$s dök först upp på %2$s."

#: inc/class-wpseo-admin-bar-menu.php:449
msgid "Facebook Debugger"
msgstr "Facebook Debugger"

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:583
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "Välj en giltig inläggstyp för taxonomi ”%s”"

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:545
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "Välj en giltig taxonomi för inläggstyp ”%s”"

#: inc/options/class-wpseo-option-titles.php:279
msgid "Error 404: Page not found"
msgstr "Fel 404: Sidan hittades inte"

#: wp-seo-main.php:500
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "Utökningen ”Standardbibliotek för PHP (SPL)” verkar inte kunna nås. Be ditt webbhotell att aktivera det."

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:49
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "%1$s innehåller en del väldigt effektiva inbyggda verktyg:"

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:13
#: src/user-meta/user-interface/custom-meta-integration.php:100
msgid "%1$s settings"
msgstr "Inställningar för %1$s"

#. translators: %s expands to the variable used for term title.
#: inc/class-upgrade.php:1558 inc/options/class-wpseo-option-titles.php:345
#: src/editors/framework/seo/terms/title-data-provider.php:27
msgid "%s Archives"
msgstr "%s-arkiv"

#: inc/class-wpseo-replace-vars.php:107
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "En ersättningsvariabel kan bara innehålla alfanumeriska tecken, ett understreck eller ett streck. Försök att döpa om din variabel."

#: admin/pages/tools.php:42
msgid "Bulk editor"
msgstr "Massredigerare"

#: src/integrations/admin/import-integration.php:119
msgid "Default settings"
msgstr "Standardinställningar"

#: admin/views/tool-bulk-editor.php:113 js/dist/new-settings.js:268
msgid "Description"
msgstr "Beskrivning"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "Exportera dina inställningar för %1$s"

#: admin/pages/tools.php:36
msgid "File editor"
msgstr "Filredigerare"

#: admin/views/tabs/tool/import-seo.php:88
#: admin/views/tool-import-export.php:24
msgid "Import"
msgstr "Importera"

#: admin/pages/tools.php:30
msgid "Import and Export"
msgstr "Importera och exportera"

#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:49
#: admin/views/tool-import-export.php:92
msgid "Import from other SEO plugins"
msgstr "Importera från andra SEO-tillägg"

#: admin/class-export.php:65 admin/views/tabs/tool/wpseo-import.php:25
#: admin/views/tabs/tool/wpseo-import.php:45
#: admin/views/tool-import-export.php:86
msgid "Import settings"
msgstr "Importera inställningar"

#: admin/views/user-profile.php:30
#: src/user-meta/framework/custom-meta/author-metadesc.php:97
msgid "Meta description to use for Author page"
msgstr "Metabeskrivning att använda för författar-sida"

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:1029
msgid "Page %1$d of %2$d"
msgstr "Sida %1$d av %2$d"

#: inc/class-wpseo-replace-vars.php:1526
msgid "Replaced with a posts custom field value"
msgstr "Ersatt med värde i ett anpassat fält för ett inlägg"

#: inc/class-wpseo-replace-vars.php:1527
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "Ersatt med ett inläggs anpassade taxonomier, kommaseparerat."

#: inc/class-wpseo-replace-vars.php:1488
msgid "Replaced with the current date"
msgstr "Ersätts med aktuellt datum"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Replaced with the current day"
msgstr "Ersätts med aktuell dag"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Replaced with the current month"
msgstr "Ersätts med aktuell månad"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "Ersatt med nuvarande sidans nummer i ordning (t ex sid 2 av 4)"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Replaced with the current year"
msgstr "Ersätts med det aktuella året"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "Ersatt med biografin från profilen för inläggets eller sidans författare"

#: admin/pages/tools.php:77
msgid "&laquo; Back to Tools page"
msgstr "&laquo; Tillbaka till sidan verktyg"

#: inc/class-wpseo-replace-vars.php:1479
msgid "Replaced with the post categories (comma separated)"
msgstr "Ersätts med inläggets kategorier (kommaseparerade)"

#: inc/class-wpseo-replace-vars.php:1484
msgid "Replaced with the term name"
msgstr "Ersätts med termens namn"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Replaced with the term description"
msgstr "Ersätts med termens beskrivning"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Attachment caption"
msgstr "Bildtext för bilaga"

#: admin/views/tabs/dashboard/dashboard.php:40
msgid "Credits"
msgstr "Erkännanden"

#: inc/class-wpseo-replace-vars.php:120
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "En ersättningsvariabel med samma namn har redan registrerats. Försök att göra variabelns namn unik."

#: admin/statistics/class-statistics-service.php:80
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "Nedan ser du SEO-poängen för dina publicerade inlägg. Nu är en lika god tid som någon att börja förbättra några av dina inlägg!"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:135 admin/views/tool-file-editor.php:235
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "Om du hade en %s-fil och den var redigerbar, kan du redigera den härifrån."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:116
msgid "You don't have a %s file, create one here:"
msgstr "Du har ingen %s-fil, skapa en här:"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:153 admin/views/tool-file-editor.php:203
msgid "If your %s were writable, you could edit it from here."
msgstr "Om din %s var skrivbar, skulle du kunna redigera den här."

#: inc/class-wpseo-replace-vars.php:1515
msgid "Replaced with the content type plural label"
msgstr "Ersatt med flertalsetiketten för aktuell inläggstyp"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Replaced with the content type single label"
msgstr "Ersatt med entalsetiketten för aktuell inläggstyp"

#. translators: %1$s resolves to Yoast SEO, %2$s resolves to the Settings
#. submenu item.
#: src/presenters/meta-description-presenter.php:35
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "Meddelande endast till administratören: Denna sida visar ingen metabeskrivning eftersom den inte har någon. Skriv antingen en specifik beskrivning för denna sida eller gå till menyn [%1$s - %2$s] och skapa en mall."

#: inc/class-wpseo-replace-vars.php:1524
msgid "Replaced with the posts focus keyphrase"
msgstr "Ersatt med inläggens fokusnyckelordsfraser"

#: admin/views/user-profile.php:26
#: src/user-meta/framework/custom-meta/author-title.php:97
msgid "Title to use for Author page"
msgstr "Sidrubrik att använda för författar-sida"

#: inc/class-wpseo-replace-vars.php:1525
msgid "Replaced with the slug which caused the 404"
msgstr "Ersatte med den slug som orsakade 404"

#: inc/class-wpseo-replace-vars.php:1528
msgid "Replaced with a custom taxonomies description"
msgstr "Ersatt med en anpassad taxonomibeskrivning"

#: admin/pages/tools.php:43
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "Detta verktyg låter dig snabbt ändra rubriker och beskrivningar för dina inlägg och sidor utan att du behöver gå in i redigeraren för respektive sida."

#: inc/class-wpseo-replace-vars.php:1474
msgid "The site's name"
msgstr "Webbplatsens namn"

#: inc/options/class-wpseo-option-titles.php:275
msgid "Page not found"
msgstr "Sidan hittades inte"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Replaced with the post/page modified time"
msgstr "Ersatt med inläggets/sidans redigeringstid"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Replaced with the post/page ID"
msgstr "Ersatt med inläggets/sidans ID"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Replaced with the post/page author's 'nicename'"
msgstr "Ersatt med smeknamnet på inläggets/sidans författare"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Replaced with the date of the post/page"
msgstr "Ersatt med inläggets/sidans datum"

#: inc/class-wpseo-replace-vars.php:1471
msgid "Replaced with the title of the post/page"
msgstr "Ersatt med inläggets/sidans rubrik"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Replaced with the category description"
msgstr "Ersatt med kategoribeskrivningen"

#: inc/class-wpseo-replace-vars.php:1476
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "Ersatt med inläggets/sidans utdrag (eller ett automatiskt genererat utdrag om inget finns angivet)"

#: inc/class-wpseo-replace-vars.php:1477
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "Ersatt med inläggets/sidans utdrag (utan automatisk generering)"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Replaced with the current page total"
msgstr "Ersatt med den nuvarande sidans totala"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Replaced with the current page number"
msgstr "Ersatt med det nuvarande sidnumret"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Replaced with the current search phrase"
msgstr "Ersatt med den nuvarande sökfrasen"

#: inc/class-wpseo-replace-vars.php:1482
msgid "Replaced with the tag description"
msgstr "Ersatt med etikettbeskrivningen"

#: inc/class-wpseo-replace-vars.php:1472
msgid "Replaced with the title of the parent page of the current page"
msgstr "Ersatt med rubriken på den överordnade sidan för den nuvarande sidan"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Replaced with the current tag/tags"
msgstr "Ersatt med nuvarande etikett/etiketter"

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "Detta verktyg låter dig snabbt ändra viktiga filer för din SEO, som din robots.txt och, om du har en, din .htaccess-fil."

#: inc/class-wpseo-replace-vars.php:110
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "En ersättningsvariabel kan inte starta med \"%%cf_\" eller \"%%ct_\" då dessa är reserverade för standardvariablarna i WPSEO och dess användarfält. Försök göra dina variabler mer unika."

#: inc/class-wpseo-replace-vars.php:124
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "Du kan inte skriva över en ersättare för en WPSEO standard variabel genom att registrera en variabel med samma namn. Använd filtret \"wpseo_replacements\" i stället för att anpassa ersättningsvärdet."

#: admin/pages/tools.php:31
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) site."
msgstr "Importera inställningar från andra SEO-tillägg och exportera dina inställningar för återanvändning på (en annan) webbplats."

#: admin/views/tabs/network/general.php:37
#: admin/views/tabs/network/general.php:43
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "Nya webbplatser i detta nätverk ärver sina SEO-inställningar från denna webbplats"

#: src/integrations/admin/first-time-configuration-integration.php:485
#: js/dist/new-settings.js:336
msgid "Person"
msgstr "Person"

#: admin/class-plugin-availability.php:77
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "Ranka bättre lokalt och i Google Maps, utan att lyfta ett finger!"

#: admin/class-yoast-network-admin.php:140
msgid "Settings Updated."
msgstr "Inställningar uppdaterade."

#: admin/views/tabs/network/general.php:27
msgid "Site Admins (default)"
msgstr "Admins för webbplatsen (standard)"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "Webbplats-ID"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "arkiverat"

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "skräppost"

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "vuxet"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:47
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "Ange det %1$sSite-ID%2$s för webbplatsen vars inställningar du vill använda som standard för alla webbplatser som läggs till ditt nätverk. Lämna tomt för ingen (d.v.s. tilläggets standardvärden kommer att användas)."

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "För att kunna skapa en omdirigering och åtgärda detta problem, behöver du %1$s. Du kan köpa tillägget, inklusive ett års support och uppdateringar, på %2$s."

#: admin/views/tabs/network/general.php:54
msgid "Take note:"
msgstr "Notera:"

#: admin/views/tabs/network/general.php:54
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "Integritetsskänsliga (FB-administratörer och liknande), temaspecifika (rubrikomskrivning) och ett fåtal väldigt webbplatsspecifika inställningar kommer inte att importeras till nya webbplatser."

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "offentlig"

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:174
msgid "%s restored to default SEO settings."
msgstr "%s återställd till standard SEO-inställningar."

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "Med hjälp av detta formulär kan du återställa en webbplats till standardinställningarna för SEO."

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "Återställ webbplats till standard"

#: admin/views/tool-bulk-editor.php:111 inc/class-wpseo-replace-vars.php:1471
#: js/dist/elementor.js:530 js/dist/externals-redux.js:1
msgid "Title"
msgstr "Rubrik"

#: admin/class-plugin-availability.php:57
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "Optimera dina videoklipp för att visa upp dem i sökresultat och få mer klick!"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:25
msgid "Who should have access to the %1$s settings"
msgstr "Vilka ska ha åtkomst till %1$s-inställningarna"

#: admin/views/tabs/network/general.php:40
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "Välj den webbplats vars inställningar du vill använda som standard för alla webbplatser som läggs till i ditt nätverk. Om du väljer ”Ingen” används tilläggets standardinställningar."

#. Translators: %s: expands to Yoast SEO Premium
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "Att skapa omdirigeringar är en %s-funktion"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:89
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "Integrera WooCommerce sömlöst med %1$s och skaffa extra funktioner!"

#: admin/views/tabs/network/general.php:28
msgid "Super Admins only"
msgstr "Endast superadministratörer"

#: inc/class-wpseo-replace-vars.php:1497
msgid "Permalink"
msgstr "Permalänk"

#: admin/class-plugin-availability.php:67
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "Är du på Google Nyheter? Öka din trafik från Google Nyheter genom att optimera för det!"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:45
msgid "The premium version of %1$s with more features & support."
msgstr "Premiumversionen av %1$s med fler funktioner och support."

#: admin/class-meta-columns.php:273
msgid "All SEO Scores"
msgstr "Alla SEO-poäng"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:186
msgid "Deactivate %s"
msgstr "Inaktivera %s"

#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
#: js/dist/ai-consent.js:4 js/dist/ai-consent.js:18 js/dist/ai-generator.js:4
#: js/dist/ai-generator.js:18 js/dist/ai-generator.js:27
#: js/dist/ai-generator.js:36 js/dist/ai-generator.js:51
#: js/dist/ai-generator.js:57 js/dist/ai-generator.js:268
#: js/dist/ai-generator.js:270 js/dist/ai-generator.js:272
#: js/dist/block-editor.js:19 js/dist/classic-editor.js:4
#: js/dist/editor-modules.js:313 js/dist/editor-modules.js:322
#: js/dist/editor-modules.js:326 js/dist/editor-modules.js:335
#: js/dist/editor-modules.js:344 js/dist/editor-modules.js:359
#: js/dist/editor-modules.js:569 js/dist/editor-modules.js:571
#: js/dist/editor-modules.js:573 js/dist/elementor.js:5
#: js/dist/externals-components.js:186 js/dist/externals-components.js:199
#: js/dist/externals-components.js:208 js/dist/externals-components.js:217
#: js/dist/externals-components.js:232 js/dist/externals-components.js:442
#: js/dist/externals-components.js:444 js/dist/externals-components.js:446
#: js/dist/externals-components.js:499 js/dist/externals/componentsNew.js:790
#: js/dist/general-page.js:6 js/dist/general-page.js:16
#: js/dist/general-page.js:17 js/dist/general-page.js:30
#: js/dist/integrations-page.js:40 js/dist/integrations-page.js:50
#: js/dist/integrations-page.js:51 js/dist/introductions.js:3
#: js/dist/new-settings.js:6 js/dist/new-settings.js:16
#: js/dist/new-settings.js:42 js/dist/redirects.js:4 js/dist/support.js:6
msgid "Close"
msgstr "Stäng"

#: admin/class-meta-columns.php:129
msgid "Meta Desc."
msgstr "Metabeskr."

#: admin/statistics/class-statistics-service.php:222
#: inc/class-wpseo-rank.php:141 inc/class-wpseo-rank.php:181
#: inc/class-wpseo-rank.php:213 js/dist/block-editor.js:534
#: js/dist/editor-modules.js:310 js/dist/editor-modules.js:574
#: js/dist/elementor.js:84 js/dist/externals-components.js:183
#: js/dist/externals/analysis.js:15 js/dist/externals/dashboardFrontend.js:4
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:1
#: js/dist/term-edit.js:1
msgid "OK"
msgstr "OK"

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:79
msgid "%s Posts Overview"
msgstr "%s inläggsöversikt"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:182
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "Tillägget %1$s kan orsaka problem när det används tillsammans med %2$s."

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:78
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "Både %1$s och %2$s kan skapa XML-webbplatskartor. Att ha två XML-webbplatskartor gör ingen skillnad för sökmotorer och kan göra din webbplats långsammare."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:71
msgid "Configure %1$s's Open Graph settings"
msgstr "Konfigurera OpenGraph-inställningar för %1$s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:67
msgid "Both %1$s and %2$s create Open Graph output, which might make Facebook, X, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "Både %1$s och %2$s skapar Open Graph-innehåll, vilket kan leda till att Facebook, X, LinkedIn och andra sociala nätverk använder fel texter och bilder när dina sidor delas."

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:308
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "Alla <span class=\"count\">(%s)</span>"
msgstr[1] "Alla <span class=\"count\">(%s)</span>"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:357
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Papperskorg <span class=\"count\">(%s)</span>"
msgstr[1] "Papperskorg <span class=\"count\">(%s)</span>"

#: admin/metabox/class-metabox.php:211
msgid "301 Redirect"
msgstr "301 omdirigering"

#: admin/class-bulk-editor-list-table.php:1045
msgid "Action"
msgstr "Åtgärd"

#: admin/metabox/class-metabox.php:202 js/dist/block-editor.js:285
#: js/dist/classic-editor.js:270 js/dist/elementor.js:389
msgid "Canonical URL"
msgstr "Kanonisk URL"

#: admin/class-bulk-editor-list-table.php:844
#: js/dist/externals/dashboardFrontend.js:4 js/dist/general-page.js:42
#: js/dist/general-page.js:49
msgid "Edit"
msgstr "Redigera"

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "Redigera filer"

#: admin/class-admin.php:229
msgid "FAQ"
msgstr "Vanliga frågor"

#: admin/class-bulk-editor-list-table.php:438
msgid "Filter"
msgstr "Filtrera"

#: admin/metabox/class-metabox.php:176
#: js/dist/externals/replacementVariableEditor.js:27
#: js/dist/externals/searchMetadataPreviews.js:62 js/dist/new-settings.js:26
#: js/dist/new-settings.js:30 js/dist/new-settings.js:35
#: js/dist/new-settings.js:39 js/dist/new-settings.js:65
#: js/dist/new-settings.js:79 js/dist/new-settings.js:109
#: js/dist/new-settings.js:138 js/dist/new-settings.js:203
#: js/dist/new-settings.js:220 js/dist/new-settings.js:229
#: js/dist/new-settings.js:266
msgid "Meta description"
msgstr "Metabeskrivning"

#: admin/metabox/class-metabox.php:196 js/dist/block-editor.js:283
#: js/dist/classic-editor.js:268 js/dist/elementor.js:387
msgid "No Archive"
msgstr "Inget arkiv"

#: admin/metabox/class-metabox.php:195 js/dist/block-editor.js:283
#: js/dist/classic-editor.js:268 js/dist/elementor.js:387
msgid "No Image Index"
msgstr "Inget bild-index"

#: admin/class-yoast-network-admin.php:43 src/config/schema-types.php:163
#: src/integrations/settings-integration.php:604 js/dist/new-settings.js:17
#: js/dist/new-settings.js:368
msgid "None"
msgstr "Ingen"

#: admin/class-bulk-editor-list-table.php:1038
msgid "Post Status"
msgstr "Inläggsstatus"

#: admin/class-bulk-editor-list-table.php:1039
msgid "Publication date"
msgstr "Publiceringsdatum"

#: admin/metabox/class-metabox.php:410
#: admin/taxonomy/class-taxonomy-metabox.php:142
#: inc/class-wpseo-admin-bar-menu.php:715
#: src/services/health-check/report-builder.php:168
msgid "SEO"
msgstr "SEO"

#: admin/class-admin.php:224 src/integrations/settings-integration.php:347
#: src/presenters/meta-description-presenter.php:37 js/dist/how-to-block.js:11
msgid "Settings"
msgstr "Inställningar"

#: admin/import/class-import-settings.php:85
msgid "Settings could not be imported:"
msgstr "Inställningarna kunde inte importeras:"

#: admin/menu/class-admin-menu.php:96 js/dist/new-settings.js:324
msgid "Tools"
msgstr "Verktyg"

#: admin/class-bulk-editor-list-table.php:866 js/dist/block-editor.js:143
#: js/dist/classic-editor.js:128 js/dist/editor-modules.js:265
#: js/dist/elementor.js:479 js/dist/externals/dashboardFrontend.js:5
#: js/dist/wincher-dashboard-widget.js:43
#: js/dist/wincher-dashboard-widget.js:112
msgid "View"
msgstr "Visa"

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:197
msgid "You can't edit %s that aren't yours."
msgstr "Du kan inte redigera %s som inte är dina."

#. translators: %s expands to post type name.
#: admin/ajax.php:185
msgid "You can't edit %s."
msgstr "Du kan inte redigera %s."

#: admin/menu/class-admin-menu.php:92
msgid "Search Console"
msgstr "Sökkonsol"

#: admin/metabox/class-metabox.php:193 js/dist/block-editor.js:283
#: js/dist/classic-editor.js:268 js/dist/elementor.js:387
msgid "Meta robots advanced"
msgstr "Meta-robotar avancerat"

#: admin/ajax.php:206
msgid "You have used HTML in your value which is not allowed."
msgstr "Du har använt HTML i ditt värde vilket är otillåtet."

#: admin/metabox/class-metabox.php:197 js/dist/block-editor.js:283
#: js/dist/classic-editor.js:268 js/dist/elementor.js:387
msgid "No Snippet"
msgstr "Ingen förhandsvisningstext"

#: admin/menu/class-network-admin-menu.php:56 admin/pages/network.php:18
#: src/general/user-interface/general-page-integration.php:191
#: js/dist/new-settings.js:366
msgid "General"
msgstr "Allmänt"

#: inc/class-wpseo-replace-vars.php:1524
msgid "Focus keyword"
msgstr "Fokusnyckelord"

#: admin/metabox/class-metabox.php:200
msgid "Title to use for this page in breadcrumb paths"
msgstr "Rubrik som används för denna sida till sökvägen för synlig sökväg"

#: admin/metabox/class-metabox.php:199 js/dist/block-editor.js:284
#: js/dist/classic-editor.js:269 js/dist/elementor.js:388
msgid "Breadcrumbs Title"
msgstr "Rubrik för synlig sökväg"

#: admin/views/class-yoast-feature-toggles.php:136 js/dist/new-settings.js:33
#: js/dist/new-settings.js:326 js/dist/new-settings.js:328
msgid "XML sitemaps"
msgstr "XML-webbplatskartor"

#. translators: %s expands to post type.
#: admin/ajax.php:173
msgid "Post has an invalid Content Type: %s."
msgstr "Inlägget har en ogiltig inläggstyp: %s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:206
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "Den kanoniska URL som sidan ska peka till. Lämna tomt för att använda permalänk som standard. Även %1$skanoniska adresser över flera domäner%2$s stöds."

#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-gutenberg-compatibility-notification.php:88
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "%1$s upptäckte att du använder version %2$s av %3$s, uppdatera till den senaste versionen för att förhindra kompatibilitetsproblem."

#: admin/ajax.php:162
msgid "Post doesn't exist."
msgstr "Inlägget finns inte."

#: admin/class-admin.php:182
msgid "Posts"
msgstr "Inlägg"

#: admin/metabox/class-metabox.php:212
msgid "The URL that this page should redirect to."
msgstr "URL som denna sida ska omdirigeras till."

#: admin/import/class-import-settings.php:121
msgid "Settings successfully imported."
msgstr "Inställningarna har importerats."

#: src/presenters/admin/search-engines-discouraged-presenter.php:33
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "Stort SEO-problem: Du blockerar åtkomst för robotar."

#: admin/class-bulk-editor-list-table.php:856
msgid "Preview"
msgstr "Förhandsgranska"

#: admin/class-bulk-editor-list-table.php:1036
msgid "WP Page Title"
msgstr "WP-sidrubrik"

#: admin/class-bulk-editor-list-table.php:1040
msgid "Page URL/Slug"
msgstr "Sidans URL/slug"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "Ny Yoast-metabeskrivning"

#: admin/class-meta-columns.php:828
msgid "Post is set to noindex."
msgstr "Inlägget är inställt till noindex."

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "Befintlig Yoast-metabeskrivning"

#: admin/class-meta-columns.php:128
msgid "SEO Title"
msgstr "SEO-rubrik"

#: admin/class-admin.php:308
#: src/user-meta/framework/additional-contactmethods/facebook.php:28
msgid "Facebook profile URL"
msgstr "URL till Facebook-profil"

#: src/services/health-check/default-tagline-runner.php:32
msgid "Just another WordPress site"
msgstr "En till WordPress-webbplats"

#: admin/metabox/class-metabox.php:181
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "Varning: även om du kan ändra inställningarna för meta-robotar här, så är hela webbplatsen inställd på ”noindex” i de övergripande integritetsinställningarna, så dessa inställningar har ingen effekt."

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:865
msgid "View &#8220;%s&#8221;"
msgstr "Visa ”%s”"

#. translators: Hidden accessibility text; %s: post title.
#: admin/class-bulk-editor-list-table.php:855
msgid "Preview &#8220;%s&#8221;"
msgstr "Förhandsgranska ”%s”"

#: admin/metabox/class-metabox.php:435
#: admin/taxonomy/class-taxonomy-metabox.php:160 js/dist/new-settings.js:39
msgid "Social"
msgstr "Socialt"